# AI_PROJECT_META.md

> **Working Agreement for AI Agents & Human Contributors**  
> This document serves as the single source of truth for understanding and safely contributing to the HustlePlug Chrome Extension project.

---

## 1. 📋 Project Overview

**Project Type**: Chrome Extension (Manifest V3) with Node.js API Backend  
**Primary Purpose**: AI-powered content analysis and social media automation with pro membership validation  
**Architecture**: Manager-based modular frontend + Express.js API backend + Turso database  

### Core Technologies
- **Frontend**: Vanilla JavaScript (ES2022+), Chrome Extension APIs
- **Backend**: Node.js 18+, Express.js, Turso (SQLite) database  
- **Deployment**: Chrome Web Store + Render.com hosting
- **Testing**: Jest, Playwright for E2E
- **Analysis Tools**: Python scripts for code quality and dependency analysis

### High-Level Architecture
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Chrome         │    │  Node.js API     │    │  Turso Database │
│  Extension      │◄──►│  (Render.com)    │◄──►│  (SQLite)       │
│  (Frontend)     │    │  (Backend)       │    │  (Cloud)        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Manager-Based Pattern**: All popup functionality extends `BaseManager` and registers with `PopupController` for centralized coordination.

---

## 2. 📁 Folder and File Organization

### Root Structure
```
hustleplug/
├── js/                     # 🟢 Extension JavaScript modules
│   ├── auth/              # Authentication & pro key validation
│   ├── integrations/      # Platform integrations (Telegram, Discord)
│   ├── popup/             # 🎯 Manager-based popup architecture
│   │   ├── core/          # PopupController, BaseManager
│   │   ├── ui/            # UI state management
│   │   ├── settings/      # Settings & configuration
│   │   ├── analysis/      # AI analysis logic
│   │   └── [features]/    # Feature-specific managers
│   ├── security/          # Crypto, hashing utilities
│   ├── user/              # User management
│   └── utils/             # Shared utilities
├── vercel-api/            # 🟢 Node.js API backend
│   ├── api/               # Express route handlers
│   ├── db/                # Turso database queries
│   ├── services/          # Business logic services
│   └── test-*.js          # API integration tests
├── styles/                # CSS with modular organization
├── tests/                 # Browser & E2E tests
├── admin/                 # Admin tools & documentation
├── *.py                   # 🟢 Code analysis tools (complexity, dependency mapping)
├── popup.html/js          # Main extension entry points
├── background.js          # Service worker (⚠️ 835 lines - needs refactoring)
└── manifest.json          # Chrome extension configuration
```

### File Size Limits
- **Hard Limit**: 500 lines per file (managers: 300 lines)
- **Function Limit**: 100 lines maximum
- **Class Limit**: 250 lines maximum
- **⚠️ Current Violations**: `background.js` (835 lines), `popup-original.js` (3302 lines - legacy)

### Co-location Rules
- **Managers**: Logic + tests in same directory
- **Styles**: Component-specific CSS in `styles/components/`
- **Tests**: `test-*.js` pattern for API tests, `/tests/` for E2E

---

## 3. 🛠️ Language and Coding Standards

### JavaScript/TypeScript Standards
- **ES2022+ Only**: Use `import`/`export`, `async/await`, top-level await
- **Module Type**: `"type": "module"` in package.json
- **Named Exports Preferred**: Avoid default exports except for single-purpose files
- **ESLint Configuration**: `eslint.config.js` with SonarJS rules enforced

### Linting & Quality Tools
```bash
# Linting (enforced in CI)
npm run lint                    # ESLint with SonarJS rules
python code_smell_detector.py   # Custom code smell detection
python complexity_analyzer.py   # Cyclomatic complexity analysis
```

### Naming Conventions
- **Files**: `kebab-case.js` (e.g., `rate-limiter.js`)
- **Classes**: `PascalCase` (e.g., `SettingsManager`)
- **Functions**: `camelCase` (e.g., `validateProKey`)
- **Constants**: `UPPER_SNAKE_CASE` (e.g., `API_ENDPOINT`)

### Manager Pattern Requirements
```javascript
// ✅ REQUIRED: All popup functionality extends BaseManager
import { BaseManager } from '../core/BaseManager.js';

export class FeatureManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
        // Feature-specific initialization
    }

    cleanup() {
        // Clean up resources
        super.cleanup();
    }
}
```

---

## 4. 🔐 Security and Secrets Management

### Secrets Storage
- **Extension**: Chrome sync storage for API keys (encrypted)
- **Backend**: Environment variables via `.env` (never commit)
- **Production**: Render.com environment variables

### Required Environment Variables
```bash
# Backend API (vercel-api/)
TURSO_DATABASE_URL=libsql://your-database-url
TURSO_AUTH_TOKEN=your-auth-token
NODE_ENV=production
```

### Security Rules
- **🚫 NEVER**: Store plain-text API keys or credentials
- **✅ ALWAYS**: Hash keys with salt using `crypto.createHash('sha256')`
- **✅ ALWAYS**: Use parameterized SQL queries (never string concatenation)
- **✅ ALWAYS**: Validate inputs before processing

### Input Validation
```javascript
// ✅ Good: Validate and sanitize inputs
function validateApiKey(key) {
    if (!key || typeof key !== 'string' || key.length < 10) {
        throw new Error('Invalid API key format');
    }
    return key.trim();
}
```

---

## 5. 🧪 Testing Requirements

### Test Organization
```
tests/
├── ui/                     # Playwright E2E tests
├── bg/                     # Background script tests (Jest)
└── integration/            # Cross-component tests

vercel-api/
├── test-*.js              # API endpoint tests
├── test-*-debug.js        # Debugging specific issues
└── test-*-comprehensive.js # Full test suites
```

### Testing Frameworks
- **API Tests**: Node.js built-in test runner + custom assertions
- **E2E Tests**: Playwright (planned)
- **Unit Tests**: Jest (planned)

### Test Commands
```bash
# Backend API tests
cd vercel-api
npm run test-api

# Run specific test file
node test-rate-limit-comprehensive.js

# Python analysis tools
python -m pytest python-tooling/tests/  # (when implemented)
```

### Test Structure Pattern
```javascript
// ✅ Standard test structure
async function testFeatureName() {
    console.log('🧪 Testing Feature Name\n');
    
    try {
        // Setup
        const testData = setupTestData();
        
        // Execute
        const result = await functionUnderTest(testData);
        
        // Verify
        console.log(`✅ Expected behavior: ${result.success}`);
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}
```

---

## 6. 💻 Development Workflow

### Local Development Setup
```bash
# 1. Clone repository
git clone <repo-url>
cd hustleplug

# 2. Install backend dependencies
cd vercel-api
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 4. Load extension in Chrome
# - Open chrome://extensions/
# - Enable Developer mode
# - Click "Load unpacked" and select project root

# 5. Start backend API (optional for local testing)
npm run dev  # Starts on localhost:3000
```

### Common Commands
```bash
# Extension development
./package-extension.ps1        # Package for distribution (Windows)
./package-extension.bat        # Package for distribution (Windows)

# Backend development
cd vercel-api
npm run dev                    # Start with nodemon
npm run start                  # Production start
npm run deploy                 # Deploy to Render

# Code analysis
python complexity_analyzer.py          # Analyze code complexity
python dependency_visualizer.py        # Generate dependency graphs
python code_smell_detector.py          # Detect code smells
```

### Required Tools
- **Node.js**: 18+ with npm
- **Python**: 3.8+ for analysis tools
- **Chrome**: Latest version for testing
- **Git**: For version control

---

## 7. 🔄 Git and Version Control

### Branching Model
- **Main Branch**: `main` (protected, requires PR)
- **Feature Branches**: `feature/description` or `fix/description`
- **No Direct Commits**: All changes via Pull Requests

### Commit Message Style
```bash
# Conventional Commits format
feat: add new analysis manager for crypto content
fix: resolve rate limiting issue in API validation
docs: update installation guide with new requirements
refactor: split background.js into smaller modules
test: add comprehensive rate limiting tests
```

### Pre-commit Checks
- **Linting**: ESLint must pass (zero errors)
- **File Size**: No files over 500 lines (managers: 300 lines)
- **Tests**: Relevant tests must pass
- **Security**: No hardcoded secrets or credentials

---

## 8. 📚 Documentation Standards

### Inline Documentation
```javascript
/**
 * Validates pro membership key and returns membership details
 * @param {string} plainKey - Plain text key to validate
 * @param {boolean} forceValidation - Skip cache if true
 * @returns {Promise<Object>} Validation result with membership details
 */
async function validateProKey(plainKey, forceValidation = false) {
    // Implementation
}
```

### Manager Documentation
```javascript
/**
 * Settings Manager
 * Handles all settings-related operations including API keys,
 * membership management, and integration configurations
 * @extends BaseManager
 */
export class SettingsManager extends BaseManager {
    // Class implementation
}
```

### Documentation Locations
- **API Docs**: `TOOLS_API_DOCUMENTATION.md`
- **Setup Guides**: `INSTALLATION.md`, `turso-setup.md`
- **Change Logs**: `SESSION_CHANGES_*.md`
- **Architecture**: `JAVASCRIPT_MODULAR_MIGRATION_GUIDE.md`

---

## 9. ⚡ Performance Guidelines

### Database Performance
- **Use Turso batch operations** for atomic transactions
- **Implement connection pooling** for efficiency
- **Add indexes** on frequently queried columns
- **Avoid N+1 queries** through proper query design

### Extension Performance
- **Debounce API calls** to prevent excessive requests
- **Cache responses** using Chrome storage API
- **Lazy load managers** to reduce startup time
- **Clean up event listeners** in manager cleanup methods

### Memory Management
```javascript
// ✅ Good: Proper cleanup in managers
cleanup() {
    // Remove event listeners
    document.removeEventListener('click', this.clickHandler);
    
    // Clear timers
    if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
    }
    
    // Call parent cleanup
    super.cleanup();
}
```

---

## 10. 🚀 Deployment and Release Process

### Chrome Extension Release
```bash
# 1. Update version in manifest.json (semver)
# 2. Test extension thoroughly
# 3. Package extension
./package-extension.ps1

# 4. Upload to Chrome Web Store
# 5. Submit for review
```

### Backend API Deployment
- **Platform**: Render.com
- **Trigger**: Push to `main` branch (auto-deploy)
- **Environment**: Production environment variables required
- **Health Check**: `/health` endpoint available

### Pre-release Checklist
- [ ] All tests passing
- [ ] No file size violations
- [ ] ESLint passing with zero errors
- [ ] API endpoints responding correctly
- [ ] Rate limiting functioning
- [ ] Database migrations applied
- [ ] Environment variables configured

---

## 11. ✅ Code Review Checklist

### Architecture & Design
- [ ] **Manager Pattern**: New popup features extend BaseManager
- [ ] **Single Responsibility**: Each file/class has one clear purpose
- [ ] **Dependency Injection**: Managers receive controller in constructor
- [ ] **Backward Compatibility**: Existing APIs continue to work

### Security & Quality
- [ ] **No Hardcoded Secrets**: All secrets in environment variables
- [ ] **Input Validation**: User inputs are validated and sanitized
- [ ] **SQL Safety**: Parameterized queries only, no string concatenation
- [ ] **Error Handling**: Graceful degradation with proper error messages

### Code Standards
- [ ] **File Size Limits**: Files under 500 lines (managers: 300)
- [ ] **Function Size**: Functions under 100 lines
- [ ] **ESLint Clean**: No linting errors or warnings
- [ ] **Proper Imports**: ES6 imports/exports used correctly

### Testing & Documentation
- [ ] **Tests Included**: New functionality has appropriate tests
- [ ] **Documentation Updated**: JSDoc comments for public methods
- [ ] **API Documentation**: Endpoint changes documented
- [ ] **Migration Guide**: Breaking changes include migration path

---

## 12. 🐛 Debugging and Monitoring

### Logging Standards
```javascript
// ✅ Consistent logging format with emojis
console.log('🔍 Validation request from IP:', clientIP);      // Info
console.warn('⚠️ Usage logging failed, continuing...');       // Warning
console.error('❌ Validation error:', error);                 // Error
console.log('✅ Valid key used:', notes.substring(0, 30));    // Success
console.log('🚫 Rate limit exceeded for IP:', ip);            // Rate limit
console.log('📊 Usage logged: Key ID', id);                   // Analytics
```

### Debug Tools
```bash
# Extension debugging
chrome://extensions/        # Extension management
chrome://inspect/           # DevTools for background scripts

# API debugging
node test-api-validation.js        # API endpoint testing
node debug-pro-key-issue.js        # Specific issue debugging

# Code analysis
python complexity_analyzer.py      # Complexity metrics
python dependency_visualizer.py    # Dependency graphs
```

### Production Monitoring
- **Health Endpoint**: `GET /health` for API status
- **Rate Limiting**: Monitor 429 responses
- **Error Tracking**: Structured error logs
- **Performance**: Response time tracking

---

## 13. 🚨 Critical Violations Summary

### Security Violations (Zero Tolerance)
- 🚫 **Plain-text credential storage** - Hash all keys with salt
- 🚫 **SQL injection possibilities** - Use parameterized queries only
- 🚫 **Missing input validation** - Validate all user inputs
- 🚫 **Hardcoded secrets** - Use environment variables only

### Architecture Violations (Immediate Refactoring Required)
- 🚫 **File size exceeding 500 lines** - Split into modules
- 🚫 **Mixed concerns in single file** - Separate by domain
- 🚫 **Breaking public APIs** - Maintain backward compatibility
- 🚫 **Bypassing manager pattern** - All popup features must use managers

### Code Quality Violations (Fix Before Merge)
- 🚫 **ESLint errors** - Must have zero linting errors
- 🚫 **Missing tests for new features** - Include appropriate tests
- 🚫 **Undocumented public methods** - Add JSDoc comments
- 🚫 **Memory leaks** - Implement proper cleanup methods

---

## 14. 🧠 Pattern Awareness for AI Agents

### Before Making Changes
1. **🔍 Search Existing Code**: Always check for similar functionality
   ```bash
   # Search for existing patterns
   rg "validateProKey" js/
   rg "class.*Manager" js/popup/
   ```

2. **📋 Check Dependencies**: Understand manager relationships
   ```javascript
   // Check PopupController registration order
   // Understand manager dependencies
   ```

3. **🧪 Verify Tests**: Run relevant tests before changes
   ```bash
   node test-verification-delay-fix.js
   ```

### Reuse Existing Utilities
- **Authentication**: Use `js/auth/` modules
- **Storage**: Use `js/utils/storage.js` helpers
- **UI Updates**: Use `UIManager` methods
- **Settings**: Use `SettingsManager` for configuration

### Manager Registration Pattern
```javascript
// ✅ ALWAYS: Register managers in dependency order
async init() {
    // Core managers first
    this.controller.registerManager('dataManager', new DataManager(this.controller));
    this.controller.registerManager('uiManager', new UIManager(this.controller));
    
    // Feature managers
    this.controller.registerManager('settingsManager', new SettingsManager(this.controller));
    this.controller.registerManager('analysisManager', new AnalysisManager(this.controller));
    
    // Event manager last
    this.controller.registerManager('eventManager', new EventManager(this.controller));
    
    await this.controller.init();
}
```

### Confirmation Requirements
**🚨 AI AGENTS MUST CONFIRM UNDERSTANDING** before making structural changes:
- Adding new managers or modifying manager registration
- Changing authentication or security-related code
- Modifying database queries or API endpoints
- Altering the manager pattern or PopupController logic

---

## 🏁 Quick Reference

### Key Files to Understand
- `js/popup/core/PopupController.js` - Central coordinator
- `js/popup/core/BaseManager.js` - Base class for all managers
- `vercel-api/app.js` - API server entry point
- `background.js` - Extension service worker (⚠️ needs refactoring)
- `manifest.json` - Extension configuration

### Common Tasks
- **Add new feature**: Create manager in `js/popup/[feature]/`
- **API endpoint**: Add to `vercel-api/api/`
- **Database query**: Add to `vercel-api/db/`
- **Test**: Create `test-[feature].js` in appropriate location

### Emergency Contacts
- **File Size Violations**: Check `REFACTORING_TOOLKIT_INDEX.md`
- **Security Issues**: Review `KEY_MANAGEMENT_GUIDE.md`
- **Manager Pattern**: See `JAVASCRIPT_MODULAR_MIGRATION_GUIDE.md`

---

*This document is updated quarterly. Last updated: June 2025* 