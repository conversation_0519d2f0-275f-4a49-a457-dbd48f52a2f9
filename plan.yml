plan_name: Implement Help Section
objective: To create a help section accessible via the existing "Help" link in the extension popup, providing users with simple usage instructions.
context_summary: The extension popup is built with HTML (`popup.html`) and JavaScript (`popup.js`). `popup.html` contains a footer with a "Help" link (`helpLink`). `popup.js` manages showing and hiding different content sections using a `showSection()` function. A placeholder `showHelp()` function and an event listener for `helpLink` already exist. The plan is to create a new section for help content in the HTML and wire it up in the JavaScript.
steps:
  - id: step_1
    description: "Create the HTML structure for the help section in `popup.html`. This section will be hidden by default and will contain a header, a back button, and the help content."
    files:
      - hustleplug/popup.html
    notes: "The new section will be modeled after existing sections like `analysisHistorySection` for consistency. The ID will be `helpSection`."

  - id: step_2
    description: "Update `popup.js` to handle the display of the new help section."
    files:
      - hustleplug/popup.js
    notes: "This involves three changes:
    1. Add `'helpSection'` to the array of section IDs in the `showSection` function.
    2. Modify the `showHelp` function to call `this.showSection('helpSection')`.
    3. Add an event listener for the new back button (`backToActionsFromHelp`) to navigate the user back to the main actions view."
unknowns_or_questions:
  - "The exact wording for the help text can be refined, but I will use the user's provided text as a starting point." 