# 🚀 Agent Hustle Pro Analyzer - Installation Guide

This guide will walk you through installing and setting up the Agent Hustle Pro Analyzer Chrome extension.

## 📋 Prerequisites

Before installing, make sure you have:

- **Google Chrome** browser (version 88 or higher)
- **Agent Hustle API Key** (get one from your Agent Hustle dashboard)
- **Internet connection** for AI analysis features

## 🔧 Installation Methods

### Method 1: Developer Mode Installation (Current)

Since the extension is not yet published to the Chrome Web Store, you'll need to install it in developer mode:

#### Step 1: Download the Extension
1. Download or clone this repository to your computer
2. Extract the files if downloaded as a ZIP
3. Note the location of the extension folder

#### Step 2: Enable Developer Mode
1. Open Google Chrome
2. Navigate to `chrome://extensions/`
3. In the top-right corner, toggle **"Developer mode"** to ON
4. You should see additional buttons appear

#### Step 3: Load the Extension
1. Click the **"Load unpacked"** button
2. Navigate to and select the extension folder
3. Click **"Select Folder"** (or "Open" on Mac)
4. The extension should now appear in your extensions list

#### Step 4: Pin the Extension (Optional)
1. Click the puzzle piece icon (🧩) in Chrome's toolbar
2. Find "Agent Hustle Pro Analyzer" in the list
3. Click the pin icon (📌) to pin it to your toolbar

### Method 2: Chrome Web Store (Coming Soon)

Once published to the Chrome Web Store:

1. Visit the Chrome Web Store
2. Search for "Agent Hustle Pro Analyzer"
3. Click "Add to Chrome"
4. Confirm the installation

## ⚙️ Initial Setup

### Step 1: Get Your API Key

1. **Sign up** for Agent Hustle if you haven't already
2. **Log in** to your Agent Hustle dashboard
3. **Navigate** to the API section
4. **Copy** your API key

### Step 2: Configure the Extension

1. **Click** the Agent Hustle extension icon in your Chrome toolbar
2. **Enter** your API key in the configuration field
3. **Click** "Save"
4. **Verify** the status shows "API Key configured"

### Step 3: Test the Extension

1. **Navigate** to any webpage with text content
2. **Select** some text (at least 10 characters)
3. **Right-click** and look for "🚀 Agent Hustle Pro Analyzer" in the context menu
4. **Choose** an analysis option to test

## 🎯 Verification Steps

### Check Extension is Loaded
- Extension appears in `chrome://extensions/`
- Extension icon visible in toolbar (if pinned)
- No error messages in the extensions page

### Check API Configuration
- Click extension icon
- See "API Key configured" status
- No error messages when saving API key

### Check Functionality
- Right-click context menu shows Agent Hustle options
- Analysis requests complete successfully
- Results panel appears on the page

## 🐛 Troubleshooting Installation

### Extension Won't Load

**Problem**: "Load unpacked" fails or shows errors

**Solutions**:
- Ensure you selected the correct folder (containing `manifest.json`)
- Check that all required files are present
- Verify `manifest.json` is valid JSON
- Try refreshing the extensions page

### Extension Loads but Doesn't Work

**Problem**: Extension appears but features don't work

**Solutions**:
- Check the browser console for errors (F12 → Console)
- Verify your API key is correct
- Ensure you have internet connection
- Try reloading the extension

### Context Menu Not Appearing

**Problem**: Right-click doesn't show Agent Hustle options

**Solutions**:
- Refresh the webpage after installing
- Check if you're on a supported page (not chrome:// pages)
- Verify extension permissions are granted
- Try on a different website

### API Key Issues

**Problem**: API key won't save or shows as invalid

**Solutions**:
- Double-check your API key for typos
- Ensure the API service is running
- Verify your API key has proper permissions
- Try copying the key again from the dashboard

## 📁 File Structure Verification

Ensure your extension folder contains these files:

```
agent-hustle-extension/
├── manifest.json          ✅ Required
├── popup.html             ✅ Required
├── popup.js               ✅ Required
├── background.js          ✅ Required
├── content.js             ✅ Required
├── content.css            ✅ Required
├── styles/
│   └── popup.css          ✅ Required
├── icons/                 📁 Optional (for custom icons)
├── README.md              📄 Documentation
└── INSTALLATION.md        📄 This file
```

## 🔒 Permissions Explained

The extension requests these permissions:

- **activeTab**: To analyze content on the current tab
- **storage**: To securely store your API key
- **contextMenus**: To add right-click menu options
- **scripting**: To inject analysis results into pages
- **host_permissions**: To communicate with Agent Hustle API

All permissions are used only for the extension's core functionality.

## 🔄 Updating the Extension

### For Developer Mode Installation:

1. Download the latest version
2. Go to `chrome://extensions/`
3. Click the refresh icon (🔄) on the extension card
4. Or remove and re-add the extension

### For Chrome Web Store Installation:

Extensions update automatically, but you can force an update:

1. Go to `chrome://extensions/`
2. Toggle "Developer mode" on and off
3. Extensions will check for updates

## 🆘 Getting Help

If you encounter issues during installation:

### Check Common Solutions
1. **Restart Chrome** and try again
2. **Disable other extensions** temporarily to check for conflicts
3. **Clear browser cache** and cookies
4. **Try incognito mode** to test without other extensions

### Gather Information
Before seeking help, collect:
- Chrome version (`chrome://version/`)
- Operating system
- Error messages (screenshots helpful)
- Steps that led to the issue

### Contact Support
- Check the troubleshooting section in README.md
- Review the browser console for error messages
- Provide detailed information about your setup and the issue

## ✅ Installation Complete!

Once installed and configured, you can:

- **Right-click** on any text or page for analysis options
- **Use keyboard shortcuts** for quick analysis
- **Click the extension icon** for advanced features
- **Enjoy professional AI analysis** at your fingertips!

## Advanced Features

### Enhanced Prompt Management

The prompt cards have been enhanced with additional functionality for improved productivity:

#### New Button Features:
- **🚀 Use Prompt** - Load prompt into analysis form
- **📋 Copy Content** - Copy prompt text to clipboard  
- **📄 Duplicate** - Create a copy of the prompt
- **📌/📍 Pin/Unpin** - Toggle prompt priority status
- **✏️ Edit** - Modify prompt content and settings
- **🗑️ Delete** - Remove prompt permanently

#### Keyboard Shortcuts:
When focused on a prompt card, you can use:
- `Ctrl + U` - Use prompt
- `Ctrl + C` - Copy prompt content to clipboard
- `Ctrl + D` - Duplicate prompt  
- `Ctrl + E` - Edit prompt
- `Ctrl + P` - Toggle pin status
- `Enter` - Activate tag filters

#### Accessibility Features:
- Full keyboard navigation support
- ARIA labels for screen readers
- Focus indicators for all interactive elements
- Hover tooltips showing full prompt content

#### Enhanced Visual Design:
- Color-coded button hover states
- Smooth animations and transitions
- Improved button accessibility
- Enhanced prompt content preview

### Backward Compatibility
All existing functionality remains unchanged. The enhanced features are additive and don't affect any existing workflows or data.

---

**🚀 Welcome to Agent Hustle Pro Analyzer!** You're now ready to analyze web content with professional AI capabilities. 