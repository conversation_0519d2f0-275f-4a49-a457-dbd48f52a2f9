// Agent Hustle Pro Analyzer - Popup Script
import { API_ENDPOINT, PAGINATION_CONFIG } from './config.js';
import { checkProStatus, setProKey, getMaskedProKey, getDetailedProStatus, getMembershipTimeRemaining, removeProKey, refreshProStatus } from './js/user/proStatus.js';
import { runDailyMembershipCheck } from './js/utils/membershipChecker.js';
import { promptManager } from './js/user/promptManager.js';
import { PaginationManager } from './js/utils/pagination.js';
import { 
    saveTelegramSettings, 
    getTelegramSettings, 
    clearTelegramSettings, 
    isTelegramConfigured,
    getMaskedBotToken,
    saveTelegramAutoSendSettings,
    getTelegramAutoSendSettings,
    updateTelegramAutoSendFailureCount,
    updateTelegramAutoSendSuccess
} from './js/user/telegramSettings.js';
import { 
    sendAnalysisToTelegram, 
    testTelegramConnection 
} from './js/integrations/telegram.js';
import { 
    saveDiscordSettings, 
    getDiscordSettings, 
    clearDiscordSettings, 
    isDiscordConfigured,
    getMaskedWebhookUrl,
    saveDiscordAutoSendSettings,
    getDiscordAutoSendSettings,
    updateDiscordAutoSendFailureCount,
    updateDiscordAutoSendSuccess
} from './js/user/discordSettings.js';
import { 
    sendAnalysisToDiscord, 
    testDiscordWebhook 
} from './js/integrations/discord.js';

class AgentHustleAnalyzer {
    constructor() {
        this.baseUrl = API_ENDPOINT;
        this.apiKey = null;
        this.sessionId = `session-${Date.now()}`;
        this.currentAnalysis = null;
        this.currentEditingPrompt = null;
        this.currentFilterTag = null;
        
        // Initialize pagination managers
        this.historyPagination = new PaginationManager({
            itemsPerPage: PAGINATION_CONFIG.HISTORY_ITEMS_PER_PAGE,
            containerId: 'historyPagination',
            onPageChange: () => this.loadAndDisplayAnalysis()
        });
        
        this.promptPagination = new PaginationManager({
            itemsPerPage: PAGINATION_CONFIG.PROMPTS_ITEMS_PER_PAGE,
            containerId: 'promptPagination',
            onPageChange: () => this.refreshPromptList()
        });
        
        this.init();
    }

    async init() {
        await this.loadApiKey();
        this.setupEventListeners();
        this.updateUI();
        
        // Initialize prompt manager
        await this.loadSavedPromptsSelect();
        
        // Run daily membership check on popup open
        try {
            await runDailyMembershipCheck();
        } catch (membershipError) {
            console.warn('Membership check failed:', membershipError);
            // Don't block initialization if membership check fails
        }
        
        // Check for pending analysis from content script
        await this.checkPendingAnalysis();
        
        // Check for context menu actions
        await this.checkContextMenuActions();
        
        // Listen for messages from content script
        this.setupMessageListeners();
        
        // Setup event delegation for dynamic elements
        this.setupEventDelegation();
    }

    // API Key Management
    async loadApiKey() {
        try {
            const result = await chrome.storage.sync.get(['agentHustleApiKey']);
            this.apiKey = result.agentHustleApiKey;
        } catch (error) {
            console.error('Error loading API key:', error);
        }
    }

    async saveApiKey(apiKey) {
        try {
            await chrome.storage.sync.set({ agentHustleApiKey: apiKey });
            this.apiKey = apiKey;
            this.updateApiStatus();
            this.updateUI();
        } catch (error) {
            console.error('Error saving API key:', error);
            this.showError('Failed to save API key');
        }
    }

    // UI Management
    updateUI() {
        const hasApiKey = !!this.apiKey;
        
        document.getElementById('apiKeySection').style.display = hasApiKey ? 'none' : 'block';
        document.getElementById('actionsSection').style.display = hasApiKey ? 'block' : 'none';
        
        if (hasApiKey) {
            this.updateApiStatus();
        }
    }

    updateApiStatus() {
        const statusElement = document.getElementById('apiStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const text = statusElement.querySelector('.status-text');
        
        if (this.apiKey) {
            indicator.classList.add('connected');
            text.textContent = 'API Key configured';
        } else {
            indicator.classList.remove('connected');
            text.textContent = 'API Key not configured';
        }
    }

    showSection(sectionId) {
        // Hide all sections
        const sections = ['apiKeySection', 'actionsSection', 'customForm', 'resultsSection', 'loadingSection', 'analysisHistorySection', 'helpSection', 'aboutSection', 'upgradeSection', 'promptManagementSection', 'settingsSection'];
        sections.forEach(id => {
            const el = document.getElementById(id);
            if (el) { // Check if element exists before hiding
                el.style.display = 'none';
            }
        });
        
        // Hide modal if open
        const modal = document.getElementById('promptEditorModal');
        if (modal) {
            modal.style.display = 'none';
        }
        
        // Show target section
        const targetEl = document.getElementById(sectionId);
        if (targetEl) {
            targetEl.style.display = 'block';
        }
        
        // Load settings content if showing settings
        if (sectionId === 'settingsSection') {
            this.loadSettingsContent();
        }
    }

    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <div style="background: #dc3545; color: white; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px;">
                ❌ ${message}
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }

    showSuccess(message) {
        // Create success notification
        const successDiv = document.createElement('div');
        successDiv.className = 'success-notification';
        successDiv.innerHTML = `
            <div style="background: #28a745; color: white; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px;">
                ✅ ${message}
            </div>
        `;
        
        document.body.appendChild(successDiv);
        
        setTimeout(() => {
            successDiv.remove();
        }, 3000);
    }

    showMembershipWarning(warning) {
        // Create membership warning notification
        const warningDiv = document.createElement('div');
        warningDiv.className = 'membership-warning-notification';
        warningDiv.innerHTML = `
            <div style="background: ${warning.urgent ? '#dc3545' : '#ffc107'}; color: ${warning.urgent ? 'white' : '#212529'}; padding: 12px; border-radius: 8px; margin: 10px 20px; font-size: 14px; display: flex; align-items: center; justify-content: space-between;">
                <span>${warning.message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0 5px;">×</button>
            </div>
        `;
        
        document.body.appendChild(warningDiv);
        
        // Auto-hide after 10 seconds for non-urgent warnings
        if (!warning.urgent) {
            setTimeout(() => {
                if (warningDiv && warningDiv.parentNode) {
                    warningDiv.remove();
                }
            }, 10000);
        }
    }

    // Event Listeners
    setupEventListeners() {
        // API Key Management
        document.getElementById('saveApiKey').addEventListener('click', async () => {
            const apiKey = document.getElementById('apiKeyInput').value.trim();
            if (apiKey) {
                await this.saveApiKey(apiKey);
                this.showSuccess('API Key saved successfully!');
            } else {
                this.showError('Please enter a valid API key');
            }
        });

        // Quick Actions
        document.getElementById('analyzeSelection').addEventListener('click', () => {
            this.analyzeSelection();
        });

        document.getElementById('analyzePage').addEventListener('click', () => {
            this.analyzePage();
        });

        document.getElementById('customAnalysis').addEventListener('click', async () => {
            await this.handleCustomAnalysisClick();
        });

        document.getElementById('viewAnalysisHistory').addEventListener('click', () => {
            this.showSection('analysisHistorySection');
            this.loadAndDisplayAnalysis();
        });

        document.getElementById('managePrompts').addEventListener('click', async () => {
            await this.handlePromptManagerClick();
        });

        // Custom Analysis Form
        document.getElementById('backToActions').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('runCustomAnalysis').addEventListener('click', () => {
            this.runCustomAnalysis();
        });

        document.getElementById('loadSelectedPrompt').addEventListener('click', () => {
            this.loadSelectedPrompt();
        });

        document.getElementById('saveCurrentPrompt').addEventListener('click', () => {
            this.saveCurrentPrompt();
        });

        // Results Section
        document.getElementById('backToActionsFromResults').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('copyResults').addEventListener('click', () => {
            this.copyResults();
        });

        document.getElementById('exportResults').addEventListener('click', () => {
            this.exportResults();
        });

        // Analysis History
        document.getElementById('backToActionsFromHistory').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('backToActionsFromHelp').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('backToActionsFromAbout').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('backToActionsFromSettings').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        // Pro Upgrade Section
        document.getElementById('backToActionsFromUpgrade').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        // Prompt Management Section
        document.getElementById('backToActionsFromPrompts').addEventListener('click', () => {
            this.showSection('actionsSection');
        });

        document.getElementById('promptSearch').addEventListener('input', (e) => {
            this.searchPrompts(e.target.value);
        });

        document.getElementById('promptSort').addEventListener('change', (e) => {
            this.sortPrompts(e.target.value);
        });

        document.getElementById('addNewPrompt').addEventListener('click', async () => {
            await this.openPromptEditor();
        });

        document.getElementById('exportPrompts').addEventListener('click', () => {
            this.exportPrompts();
        });

        document.getElementById('importPrompts').addEventListener('click', () => {
            document.getElementById('importPromptsFile').click();
        });

        document.getElementById('importPromptsFile').addEventListener('change', (e) => {
            this.importPrompts(e.target.files[0]);
        });

        // Prompt Editor Modal
        document.getElementById('closePromptEditor').addEventListener('click', () => {
            this.closePromptEditor();
        });

        document.getElementById('cancelPromptEdit').addEventListener('click', () => {
            this.closePromptEditor();
        });

        document.getElementById('savePromptEdit').addEventListener('click', () => {
            this.savePromptEdit();
        });

        // Close modal when clicking outside
        document.getElementById('promptEditorModal').addEventListener('click', (e) => {
            if (e.target.id === 'promptEditorModal') {
                this.closePromptEditor();
            }
        });

        // Key Management Modal Events
        document.getElementById('closeKeyManagement').addEventListener('click', () => {
            this.closeKeyManagementModal();
        });

        // Close key management modal when clicking outside
        document.getElementById('keyManagementModal').addEventListener('click', (e) => {
            if (e.target.id === 'keyManagementModal') {
                this.closeKeyManagementModal();
            }
        });

        document.getElementById('validateProKey').addEventListener('click', async () => {
            await this.handleProKeyValidation();
        });

        // Allow Enter key to validate pro key
        document.getElementById('proKeyInput').addEventListener('keypress', async (e) => {
            if (e.key === 'Enter') {
                await this.handleProKeyValidation();
            }
        });

        // Footer Links
        document.getElementById('settingsLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.showSection('settingsSection');
        });

        document.getElementById('helpLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.showHelp();
        });

        document.getElementById('aboutLink').addEventListener('click', (e) => {
            e.preventDefault();
            this.showAbout();
        });

        // Listen for clicks on analysis history items
        document.getElementById('analysisHistoryList').addEventListener('click', (e) => {
            // Check if delete button was clicked
            const deleteBtn = e.target.closest('.delete-btn');
            if (deleteBtn) {
                e.stopPropagation(); // Prevent card click
                const analysisId = deleteBtn.dataset.analysisId;
                this.deleteAnalysis(analysisId);
                return;
            }
            
            // Handle card click for viewing analysis
            const historyItem = e.target.closest('.history-item');
            if (historyItem && historyItem.dataset.analysisId) {
                this.viewAnalysisFromHistory(historyItem.dataset.analysisId);
            }
        });
    }

    // Pro Feature Management
    async handleCustomAnalysisClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.showMembershipWarning(proStatus.expirationWarning);
                }
                
                // User is pro, show custom analysis form
                this.showSection('customForm');
            } else {
                // User is regular or expired, show upgrade page
                this.showSection('upgradeSection');
                
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    this.showError('Your Pro membership has expired. Please upgrade to continue using Pro features.');
                }
                
                // Pre-fill pro key if one exists (for re-validation)
                const maskedKey = await getMaskedProKey();
                if (maskedKey) {
                    document.getElementById('proKeyInput').placeholder = `Current key: ${maskedKey}`;
                }
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            // Default to showing upgrade page on error
            this.showSection('upgradeSection');
        }
    }

    async handleProKeyValidation() {
        const proKeyInput = document.getElementById('proKeyInput');
        const proKeyStatus = document.getElementById('proKeyStatus');
        const validateBtn = document.getElementById('validateProKey');
        
        const proKey = proKeyInput.value.trim();
        
        if (!proKey) {
            this.updateProKeyStatus('invalid', 'Please enter a pro key');
            return;
        }
        
        try {
            // Show validating state
            this.updateProKeyStatus('validating', 'Validating pro key...');
            validateBtn.disabled = true;
            validateBtn.innerHTML = '<span class="btn-icon">⏳</span>Validating...';
            
            // Validate the key
            const result = await setProKey(proKey);
            
            if (result.success && result.isPro) {
                // Success - user is now pro
                this.updateProKeyStatus('valid', result.message);
                
                // Get detailed membership info for display
                const detailedStatus = await getDetailedProStatus();
                let successMessage = 'Pro key validated! Welcome to Pro features!';
                
                // Add membership details if available
                if (detailedStatus.membershipDetails) {
                    const details = detailedStatus.membershipDetails;
                    if (details.daysRemaining > 0) {
                        successMessage += ` (${details.daysRemaining} days remaining)`;
                    }
                }
                
                // Show success and redirect to custom analysis
                setTimeout(() => {
                    this.showSuccess(successMessage);
                    this.showSection('customForm');
                    proKeyInput.value = ''; // Clear the input
                }, 1000);
                
            } else {
                // Invalid key
                this.updateProKeyStatus('invalid', result.message);
            }
            
        } catch (error) {
            console.error('Error validating pro key:', error);
            this.updateProKeyStatus('invalid', 'Validation failed. Please try again.');
        } finally {
            // Reset button state
            validateBtn.disabled = false;
            validateBtn.innerHTML = '<span class="btn-icon">🔑</span>Validate Pro Key';
        }
    }

    updateProKeyStatus(status, message) {
        const proKeyStatus = document.getElementById('proKeyStatus');
        const statusText = proKeyStatus.querySelector('.status-text');
        
        // Remove existing status classes
        proKeyStatus.classList.remove('validating', 'valid', 'invalid');
        
        // Add new status class
        if (status !== 'default') {
            proKeyStatus.classList.add(status);
        }
        
        // Update message
        statusText.textContent = message;
    }

    async handlePromptManagerClick() {
        try {
            // Run daily membership check
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (proStatus.isPro) {
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.showMembershipWarning(proStatus.expirationWarning);
                }
                
                // User is pro, show prompt management section
                this.showSection('promptManagementSection');
                this.loadPromptManagement();
            } else {
                // User is regular or expired, show upgrade page
                this.showSection('upgradeSection');
                
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    this.showError('Your Pro membership has expired. Please upgrade to continue using Pro features.');
                }
                
                // Pre-fill pro key if one exists (for re-validation)
                const maskedKey = await getMaskedProKey();
                if (maskedKey) {
                    document.getElementById('proKeyInput').placeholder = `Current key: ${maskedKey}`;
                }
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            // Default to showing upgrade page on error
            this.showSection('upgradeSection');
        }
    }

    // Analysis Functions
    async analyzeSelection() {
        try {
            this.showSection('loadingSection');
            
            // Get selected text from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => window.getSelection().toString()
            });
            
            const selectedText = results[0].result;
            
            if (!selectedText || selectedText.trim().length === 0) {
                this.showError('No text selected. Please select some text and try again.');
                this.showSection('actionsSection');
                return;
            }

            const prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:

Selected Text:
${selectedText}

Please provide a comprehensive analysis including:
1. Summary of the content
2. Key insights and takeaways
3. Any important details or patterns
4. Recommendations or next steps if applicable`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing selection:', error);
            this.showError('Failed to analyze selected text');
            this.showSection('actionsSection');
        }
    }

    async analyzePage() {
        try {
            this.showSection('loadingSection');
            
            // Get page content from active tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: () => {
                    // Extract meaningful content from the page
                    const title = document.title;
                    const url = window.location.href;
                    const metaDescription = document.querySelector('meta[name="description"]')?.content || '';
                    
                    // Get main content (try to avoid navigation, ads, etc.)
                    const contentSelectors = [
                        'main',
                        'article',
                        '.content',
                        '.post-content',
                        '.entry-content',
                        '#content',
                        '.main-content'
                    ];
                    
                    let mainContent = '';
                    for (const selector of contentSelectors) {
                        const element = document.querySelector(selector);
                        if (element) {
                            mainContent = element.innerText;
                            break;
                        }
                    }
                    
                    // Fallback to body content if no main content found
                    if (!mainContent) {
                        mainContent = document.body.innerText;
                    }
                    
                    // Limit content length to avoid API limits
                    const maxLength = 8000;
                    if (mainContent.length > maxLength) {
                        mainContent = mainContent.substring(0, maxLength) + '...';
                    }
                    
                    return {
                        title,
                        url,
                        metaDescription,
                        content: mainContent
                    };
                }
            });
            
            const pageData = results[0].result;
            
            const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}
Meta Description: ${pageData.metaDescription}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations
6. Potential use cases or applications of this information`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.showError('Failed to analyze page content');
            this.showSection('actionsSection');
        }
    }

    async runCustomAnalysis() {
        try {
            const prompt = document.getElementById('customPrompt').value.trim();
            const dataSource = document.querySelector('input[name="dataSource"]:checked').value;
            
            if (!prompt) {
                this.showError('Please enter an analysis prompt');
                return;
            }
            
            this.showSection('loadingSection');
            
            let data = '';
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (dataSource === 'selection') {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => window.getSelection().toString()
                });
                data = results[0].result;
                
                if (!data || data.trim().length === 0) {
                    this.showError('No text selected. Please select some text or choose "Full Page" option.');
                    this.showSection('customForm');
                    return;
                }
            } else {
                const results = await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    function: () => {
                        const title = document.title;
                        const content = document.body.innerText.substring(0, 8000);
                        return `Title: ${title}\n\nContent: ${content}`;
                    }
                });
                data = results[0].result;
            }
            
            const fullPrompt = `${prompt}

Data to analyze:
${data}`;
            
            await this.performAnalysis(fullPrompt, 'Custom Analysis');
            
        } catch (error) {
            console.error('Error running custom analysis:', error);
            this.showError('Failed to run custom analysis');
            this.showSection('customForm');
        }
    }

    // Core Analysis Function
    async performAnalysis(prompt, analysisType) {
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'performAnalysis',
                data: {
                    prompt,
                    analysisType
                }
            });

            if (response.error) {
                throw new Error(response.error);
            }

            this.currentAnalysis = {
                type: analysisType,
                prompt: prompt,
                result: response.result,
                timestamp: new Date().toISOString()
            };
            
            // Save the new analysis
            this.saveAnalysis(analysisType, response.result);

            this.displayResults(response.result, analysisType);
            
            // Handle auto-send after successful analysis
            await this.handleAutoSend(analysisType, response.result);
            
        } catch (error) {
            console.error('Analysis error:', error);
            this.showError(`Analysis failed: ${error.message}`);
            this.showSection('actionsSection');
        }
    }

    // Results Display
    displayResults(result, analysisType, date = new Date()) {
        this.currentAnalysis = {
            result: result,
            type: analysisType,
            timestamp: date.toISOString(),
        };

        const resultsContainer = document.getElementById('analysisResults');
        
        let content = '';
        if (typeof result === 'string') {
            content = result;
        } else if (result.content) {
            content = result.content;
        } else if (result.result) {
            content = typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2);
        } else {
            content = JSON.stringify(result, null, 2);
        }
        
        const formattedContent = this.formatAnalysisContent(content);
        
        resultsContainer.innerHTML = `
            <div class="analysis-header">
                <h4>${analysisType}</h4>
                <div class="analysis-meta">
                    <span class="timestamp">📅 ${date.toLocaleString()}</span>
                    <span class="session">🔗 Session: ${this.sessionId.slice(-8)}</span>
                </div>
            </div>
            <div class="analysis-content">
                ${formattedContent}
            </div>
            ${result.tool_calls && result.tool_calls.length > 0 ? this.formatToolCalls(result.tool_calls) : ''}
        `;
        
        this.showSection('resultsSection');
    }

    formatAnalysisContent(content, summary = false) {
        if (typeof content === 'string') {
            return summary ? content.substring(0, 100) + '...' : content;
        } else if (typeof content === 'object' && content !== null) {
            // More robust handling for object content
            let formatted = '';
            if (content.content) {
                formatted += content.content;
            }
            if (content.tool_calls) {
                formatted += `\n\nTool Calls:\n${this.formatToolCalls(content.tool_calls)}`;
            }
            return summary ? formatted.substring(0, 100) + '...' : formatted;
        }
        return summary ? 'No content preview'.substring(0, 100) + '...' : 'No content';
    }

    formatToolCalls(toolCalls) {
        if (!toolCalls || toolCalls.length === 0) {
            return '';
        }
        let markdown = '\n\n**Tool Calls:**\n';
        toolCalls.forEach(call => {
            markdown += `* Tool: ${call.tool_name}\n`;
            markdown += `  * Parameters: ${JSON.stringify(call.parameters, null, 2)}\n`;
        });
        return markdown;
    }

    convertToMarkdown(analysisData) {
        if (!analysisData) {
            return '';
        }

        const { type, date, result } = analysisData;

        let markdown = `# ${type}\n\n`;
        markdown += `**Date:** ${new Date(date).toLocaleString()}\n\n`;

        if (typeof result === 'string') {
            markdown += `## Analysis Result\n\n${result}\n`;
        } else if (typeof result === 'object' && result !== null) {
            markdown += `## Analysis Result\n\n`;
            if (result.content) {
                markdown += `${result.content}\n\n`;
            }

            if (result.tool_calls) {
                markdown += this.formatToolCalls(result.tool_calls);
            }

            const otherData = { ...result };
            delete otherData.content;
            delete otherData.tool_calls;

            if (Object.keys(otherData).length > 0) {
                markdown += `### Additional Data\n\n`;
                markdown += `\`\`\`json\n${JSON.stringify(otherData, null, 2)}\n\`\`\`\n`;
            }
        } else {
            markdown += `## Analysis Result\n\n\`\`\`json\n${JSON.stringify(result, null, 2)}\n\`\`\`\n`;
        }

        return markdown;
    }

    // Utility Functions
    async copyResults() {
        if (!this.currentAnalysis || !this.currentAnalysis.result) {
            this.showError('No results to copy');
            return;
        }
        
        try {
            const textToCopy = `${this.currentAnalysis.type}\n${'='.repeat(50)}\n\n${this.currentAnalysis.result.content || JSON.stringify(this.currentAnalysis.result, null, 2)}`;
            await navigator.clipboard.writeText(textToCopy);
            this.showSuccess('Results copied to clipboard!');
        } catch (error) {
            console.error('Copy failed:', error);
            this.showError('Failed to copy results');
        }
    }

    exportResults() {
        if (!this.currentAnalysis) {
            this.showError('No analysis to export');
            return;
        }

        try {
            const exportData = {
                type: this.currentAnalysis.type,
                date: this.currentAnalysis.timestamp,
                result: this.currentAnalysis.result
            };

            const markdownContent = this.convertToMarkdown(exportData);
            const blob = new Blob([markdownContent], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `agent-hustle-analysis-${Date.now()}.md`;
            
            document.body.appendChild(a);
            a.click();
            
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            this.showSuccess('Analysis exported successfully!');
        } catch (error) {
            console.error('Error exporting results:', error);
            this.showError('Failed to export analysis');
        }
    }

    // Auto-Send Management
    async handleAutoSend(analysisType, result) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                return; // Auto-send is Pro-only
            }

            // Get auto-send settings for both integrations
            const [telegramAutoSend, discordAutoSend] = await Promise.all([
                getTelegramAutoSendSettings(),
                getDiscordAutoSendSettings()
            ]);

            // Check if any auto-send is enabled
            if (!telegramAutoSend.enabled && !discordAutoSend.enabled) {
                return; // No auto-send enabled
            }

            // Prepare analysis data for sending
            const analysisData = {
                analysisType: analysisType,
                result: result,
                date: new Date()
            };

            // Handle auto-sends in parallel
            const autoSendPromises = [];

            if (telegramAutoSend.enabled) {
                autoSendPromises.push(this.handleTelegramAutoSend(analysisData));
            }

            if (discordAutoSend.enabled) {
                autoSendPromises.push(this.handleDiscordAutoSend(analysisData));
            }

            // Execute auto-sends
            if (autoSendPromises.length > 0) {
                await Promise.allSettled(autoSendPromises);
            }

        } catch (error) {
            console.error('Error in auto-send handler:', error);
            // Don't show error to user for auto-send failures to avoid disrupting the main flow
        }
    }

    async handleTelegramAutoSend(analysisData) {
        try {
            const settings = await getTelegramSettings();
            if (!settings || !settings.botToken || !settings.chatId) {
                throw new Error('Telegram not configured');
            }

            const result = await sendAnalysisToTelegram(analysisData, settings.botToken, settings.chatId);
            
            if (result.success) {
                await updateTelegramAutoSendSuccess();
                console.log('Telegram auto-send successful');
            } else {
                throw new Error(result.error || 'Unknown error');
            }

        } catch (error) {
            console.error('Telegram auto-send failed:', error);
            
            // Update failure count
            const currentSettings = await getTelegramAutoSendSettings();
            const newFailureCount = (currentSettings.failureCount || 0) + 1;
            await updateTelegramAutoSendFailureCount(newFailureCount);
            
            // Show user notification for auto-disable
            if (newFailureCount >= 3) {
                this.showError('Telegram auto-send disabled due to repeated failures. Please check your settings.');
            }
        }
    }

    async handleDiscordAutoSend(analysisData) {
        try {
            const settings = await getDiscordSettings();
            if (!settings || !settings.webhookUrl) {
                throw new Error('Discord not configured');
            }

            const result = await sendAnalysisToDiscord(analysisData, settings.webhookUrl);
            
            if (result.success) {
                await updateDiscordAutoSendSuccess();
                console.log('Discord auto-send successful');
            } else {
                throw new Error(result.error || 'Unknown error');
            }

        } catch (error) {
            console.error('Discord auto-send failed:', error);
            
            // Update failure count
            const currentSettings = await getDiscordAutoSendSettings();
            const newFailureCount = (currentSettings.failureCount || 0) + 1;
            await updateDiscordAutoSendFailureCount(newFailureCount);
            
            // Show user notification for auto-disable
            if (newFailureCount >= 3) {
                this.showError('Discord auto-send disabled due to repeated failures. Please check your settings.');
            }
        }
    }

    async loadSettingsContent() {
        await this.loadMembershipInfo();
        await this.loadTelegramSettings();
        await this.loadDiscordSettings();
    }

    async loadMembershipInfo() {
        const membershipContent = document.getElementById('membershipInfoContent');
        
        try {
            // Run membership check to get latest status
            await runDailyMembershipCheck();
            
            const detailedStatus = await getDetailedProStatus();
            const timeRemaining = await getMembershipTimeRemaining();
            
            if (!detailedStatus.hasKey) {
                // No pro key configured
                membershipContent.innerHTML = `
                    <div class="membership-status regular">
                        <div class="status-header">
                            <span class="status-icon">👤</span>
                            <div class="status-info">
                                <h5>Regular User</h5>
                                <p class="status-description">No Pro key configured</p>
                            </div>
                        </div>
                        <div class="membership-actions">
                            <button id="upgradeFromMembership" class="btn btn-primary btn-sm">
                                🚀 Upgrade to Pro
                            </button>
                        </div>
                    </div>
                `;
                
                // Add event listener for upgrade button
                const upgradeBtn = document.getElementById('upgradeFromMembership');
                if (upgradeBtn) {
                    upgradeBtn.addEventListener('click', () => {
                        this.showSection('upgradeSection');
                    });
                }
                return;
            }
            
            if (!detailedStatus.isPro) {
                // Pro key exists but not valid (expired or invalid)
                const isExpired = detailedStatus.expired;
                
                membershipContent.innerHTML = `
                    <div class="membership-status ${isExpired ? 'expired' : 'invalid'}">
                        <div class="status-header">
                            <span class="status-icon">${isExpired ? '⏰' : '❌'}</span>
                            <div class="status-info">
                                <h5>${isExpired ? 'Membership Expired' : 'Invalid Pro Key'}</h5>
                                <p class="status-description">${detailedStatus.message}</p>
                            </div>
                        </div>
                        ${detailedStatus.membershipDetails && isExpired ? `
                            <div class="membership-details">
                                <div class="detail-item">
                                    <label>Expired On:</label>
                                    <span>${new Date(detailedStatus.membershipDetails.expiresAt).toLocaleDateString()}</span>
                                </div>
                                <div class="detail-item">
                                    <label>Total Usage:</label>
                                    <span>${detailedStatus.membershipDetails.usageCount || 0} times</span>
                                </div>
                                <div class="detail-item">
                                    <label>Tier:</label>
                                    <span class="tier-badge">${(detailedStatus.membershipDetails.tier || 'pro').toUpperCase()}</span>
                                </div>
                            </div>
                        ` : ''}
                        <div class="membership-actions">
                            <button id="renewMembership" class="btn btn-primary btn-sm">
                                🔄 ${isExpired ? 'Renew Membership' : 'Get Valid Pro Key'}
                            </button>
                        </div>
                    </div>
                `;
                
                // Add event listener for renew button
                const renewBtn = document.getElementById('renewMembership');
                if (renewBtn) {
                    renewBtn.addEventListener('click', () => {
                        this.showSection('upgradeSection');
                    });
                }
                return;
            }
            
            // User has valid pro membership
            const details = detailedStatus.membershipDetails;
            const isLegacy = detailedStatus.legacy;
            
            membershipContent.innerHTML = `
                <div class="membership-status active">
                    <div class="status-header">
                        <span class="status-icon">✅</span>
                        <div class="status-info">
                            <h5>Pro Member Active</h5>
                            <p class="status-description">${detailedStatus.message}</p>
                        </div>
                    </div>
                    
                    ${!isLegacy && details ? `
                        <div class="membership-details">
                            <div class="detail-item">
                                <label>Membership Tier:</label>
                                <span class="tier-badge">${(details.tier || 'pro').toUpperCase()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Expires On:</label>
                                <span class="expiry-date">${new Date(details.expiresAt).toLocaleDateString()}</span>
                            </div>
                            <div class="detail-item">
                                <label>Days Remaining:</label>
                                <span class="days-remaining ${details.daysRemaining <= 7 ? 'warning' : ''}">${details.daysRemaining} days</span>
                            </div>
                            <div class="detail-item">
                                <label>Usage Count:</label>
                                <span>${details.usageCount || 0} times</span>
                            </div>
                            <div class="detail-item">
                                <label>Last Used:</label>
                                <span>${new Date(details.lastUsed).toLocaleDateString()}</span>
                            </div>
                            ${details.notes ? `
                                <div class="detail-item">
                                    <label>Notes:</label>
                                    <span class="membership-notes">${details.notes}</span>
                                </div>
                            ` : ''}
                        </div>
                        
                        ${details.daysRemaining <= 7 ? `
                            <div class="expiration-warning">
                                <span class="warning-icon">${details.daysRemaining <= 1 ? '🚨' : '⚠️'}</span>
                                <span class="warning-text">
                                    ${details.daysRemaining <= 1 ? 'Expires tomorrow!' : `Expires in ${details.daysRemaining} days`}
                                </span>
                            </div>
                        ` : ''}
                    ` : `
                        <div class="legacy-notice">
                            <span class="legacy-icon">📜</span>
                            <span class="legacy-text">Legacy Pro Key - No expiration tracking</span>
                        </div>
                    `}
                    
                    <div class="membership-actions">
                        ${!isLegacy && details && details.daysRemaining <= 30 ? `
                            <button id="renewEarlyMembership" class="btn btn-outline btn-sm">
                                🔄 Renew Early
                            </button>
                        ` : ''}
                        <button id="manageMembershipKey" class="btn btn-outline btn-sm">
                            🔑 Manage Key
                        </button>
                    </div>
                </div>
            `;
            
            // Add event listeners for action buttons
            const renewEarlyBtn = document.getElementById('renewEarlyMembership');
            if (renewEarlyBtn) {
                renewEarlyBtn.addEventListener('click', () => {
                    this.showSection('upgradeSection');
                });
            }
            
            const manageKeyBtn = document.getElementById('manageMembershipKey');
            if (manageKeyBtn) {
                manageKeyBtn.addEventListener('click', () => {
                    this.showKeyManagementModal();
                });
            }
            
        } catch (error) {
            console.error('Error loading membership info:', error);
            membershipContent.innerHTML = `
                <div class="membership-status error">
                    <div class="status-header">
                        <span class="status-icon">❌</span>
                        <div class="status-info">
                            <h5>Error Loading Membership</h5>
                            <p class="status-description">Unable to load membership information</p>
                        </div>
                    </div>
                    <div class="membership-actions">
                        <button id="retryMembershipLoad" class="btn btn-outline btn-sm">
                            🔄 Retry
                        </button>
                    </div>
                </div>
            `;
            
            // Add retry button listener
            const retryBtn = document.getElementById('retryMembershipLoad');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    this.loadMembershipInfo();
                });
            }
        }
    }

    async loadTelegramSettings() {
        const telegramContent = document.getElementById('telegramSettingsContent');
        
        try {
            // Run membership check for settings access
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (!proStatus.isPro) {
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    telegramContent.innerHTML = `
                        <div class="pro-upgrade-notice">
                            <div class="upgrade-icon">⏰</div>
                            <h4>Membership Expired</h4>
                            <p>Your Pro membership has expired. Renew to continue using Telegram integration.</p>
                            <button id="telegramRenewBtn" class="btn btn-primary btn-sm">
                                Renew Membership
                            </button>
                        </div>
                    `;
                    
                    const renewBtn = document.getElementById('telegramRenewBtn');
                    if (renewBtn) {
                        renewBtn.addEventListener('click', () => {
                            this.showSection('upgradeSection');
                        });
                    }
                    return;
                }
                
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.showMembershipWarning(proStatus.expirationWarning);
                }
                // Show pro upgrade message
                telegramContent.innerHTML = `
                    <div class="pro-upgrade-notice">
                        <div class="upgrade-icon">🔒</div>
                        <h4>Pro Feature</h4>
                        <p>Telegram integration is available for Pro users only.</p>
                        <button id="telegramUpgradeToProBtn" class="btn btn-primary btn-sm">
                            Upgrade to Pro
                        </button>
                    </div>
                `;
                
                // Add event listener for the upgrade button
                const upgradeBtn = document.getElementById('telegramUpgradeToProBtn');
                if (upgradeBtn) {
                    upgradeBtn.addEventListener('click', () => {
                        this.handleCustomAnalysisClick();
                    });
                }
                return;
            }
            
            // User is Pro, show Telegram settings
            const isConfigured = await isTelegramConfigured();
            const settings = await getTelegramSettings();
            
            if (isConfigured && settings) {
                // Show configured state
                const maskedToken = await getMaskedBotToken();
                const autoSendSettings = await getTelegramAutoSendSettings();
                
                telegramContent.innerHTML = `
                    <div class="telegram-configured">
                        <div class="config-status">
                            <span class="status-indicator connected"></span>
                            <span class="status-text">Telegram configured</span>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <label>Bot Token:</label>
                                <span class="masked-value">${maskedToken}</span>
                            </div>
                            <div class="config-item">
                                <label>Chat ID:</label>
                                <span class="masked-value">${settings.chatId}</span>
                            </div>
                        </div>
                        <div class="auto-send-section">
                            <div class="auto-send-header">
                                <h5>🚀 Auto-Send Analysis Results</h5>
                                <p class="auto-send-description">Automatically send analysis results to Telegram after completion</p>
                            </div>
                            <div class="auto-send-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="telegramAutoSendToggle" ${autoSendSettings.enabled ? 'checked' : ''}>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Enable Auto-Send</span>
                                ${autoSendSettings.enabled ? '<span class="auto-send-status enabled">✅ Enabled</span>' : '<span class="auto-send-status disabled">⏸️ Disabled</span>'}
                            </div>
                            ${autoSendSettings.failureCount > 0 ? `
                                <div class="auto-send-warning">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">${autoSendSettings.failureCount} recent failure(s). ${autoSendSettings.failureCount >= 3 ? 'Auto-send has been disabled.' : ''}</span>
                                </div>
                            ` : ''}
                            ${autoSendSettings.lastSent ? `
                                <div class="auto-send-info">
                                    <span class="info-icon">📤</span>
                                    <span class="info-text">Last sent: ${new Date(autoSendSettings.lastSent).toLocaleString()}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="config-actions">
                            <button id="testTelegramConnection" class="btn btn-outline btn-sm">🔧 Test Connection</button>
                            <button id="editTelegramSettings" class="btn btn-outline btn-sm">✏️ Edit</button>
                            <button id="clearTelegramSettings" class="btn btn-danger btn-sm">🗑️ Clear</button>
                        </div>
                    </div>
                `;
            } else {
                // Show setup form
                telegramContent.innerHTML = `
                    <div class="telegram-setup">
                        <div class="setup-instructions">
                            <h5>📋 Setup Instructions</h5>
                            <ol>
                                <li>Create a bot by messaging <a href="https://t.me/botfather" target="_blank">@BotFather</a> on Telegram</li>
                                <li>Use the command <code>/newbot</code> and follow the instructions</li>
                                <li>Copy the bot token provided by BotFather</li>
                                <li>Get your chat ID by messaging <a href="https://t.me/userinfobot" target="_blank">@userinfobot</a></li>
                            </ol>
                        </div>
                        <div class="setup-form">
                            <div class="form-group">
                                <label for="telegramBotToken">Bot Token:</label>
                                <input type="password" id="telegramBotToken" placeholder="123456789:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" class="form-input">
                                <small class="form-hint">Format: 123456789:ABC-DEF1234ghIkl-zyx57W2v1u123ew11</small>
                            </div>
                            <div class="form-group">
                                <label for="telegramChatId">Chat ID:</label>
                                <input type="text" id="telegramChatId" placeholder="123456789 or @username" class="form-input">
                                <small class="form-hint">Your personal chat ID or @username</small>
                            </div>
                            <div class="form-actions">
                                <button id="saveTelegramSettings" class="btn btn-primary">💾 Save & Test</button>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // Add event listeners for the dynamically created elements
            this.setupTelegramEventListeners();
            
        } catch (error) {
            console.error('Error loading Telegram settings:', error);
            telegramContent.innerHTML = `
                <div class="error-message">
                    <span class="error-icon">❌</span>
                    <span>Error loading Telegram settings. Please try again.</span>
                </div>
            `;
        }
    }

    async loadDiscordSettings() {
        const discordContent = document.getElementById('discordSettingsContent');
        
        try {
            // Run membership check for settings access
            await runDailyMembershipCheck();
            
            const proStatus = await checkProStatus();
            
            if (!proStatus.isPro) {
                // Show expiration message if membership expired
                if (proStatus.expired) {
                    discordContent.innerHTML = `
                        <div class="pro-upgrade-notice">
                            <div class="upgrade-icon">⏰</div>
                            <h4>Membership Expired</h4>
                            <p>Your Pro membership has expired. Renew to continue using Discord integration.</p>
                            <button id="discordRenewBtn" class="btn btn-primary btn-sm">
                                Renew Membership
                            </button>
                        </div>
                    `;
                    
                    const renewBtn = document.getElementById('discordRenewBtn');
                    if (renewBtn) {
                        renewBtn.addEventListener('click', () => {
                            this.showSection('upgradeSection');
                        });
                    }
                    return;
                }
                
                // Show expiration warning if applicable
                if (proStatus.expirationWarning) {
                    this.showMembershipWarning(proStatus.expirationWarning);
                }
                // Show pro upgrade message
                discordContent.innerHTML = `
                    <div class="pro-upgrade-notice">
                        <div class="upgrade-icon">🔒</div>
                        <h4>Pro Feature</h4>
                        <p>Discord integration is available for Pro users only.</p>
                        <button id="discordUpgradeToProBtn" class="btn btn-primary btn-sm">
                            Upgrade to Pro
                        </button>
                    </div>
                `;
                
                // Add event listener for the upgrade button
                const upgradeBtn = document.getElementById('discordUpgradeToProBtn');
                if (upgradeBtn) {
                    upgradeBtn.addEventListener('click', () => {
                        this.handleCustomAnalysisClick();
                    });
                }
                return;
            }
            
            // User is Pro, show Discord settings
            const isConfigured = await isDiscordConfigured();
            const settings = await getDiscordSettings();
            
            if (isConfigured && settings) {
                // Show configured state
                const maskedUrl = await getMaskedWebhookUrl();
                const autoSendSettings = await getDiscordAutoSendSettings();
                
                discordContent.innerHTML = `
                    <div class="discord-configured">
                        <div class="config-status">
                            <span class="status-indicator connected"></span>
                            <span class="status-text">Discord configured</span>
                        </div>
                        <div class="config-details">
                            <div class="config-item">
                                <label>Webhook URL:</label>
                                <span class="masked-value">${maskedUrl}</span>
                            </div>
                        </div>
                        <div class="auto-send-section">
                            <div class="auto-send-header">
                                <h5>🚀 Auto-Send Analysis Results</h5>
                                <p class="auto-send-description">Automatically send analysis results to Discord after completion</p>
                            </div>
                            <div class="auto-send-toggle">
                                <label class="toggle-switch">
                                    <input type="checkbox" id="discordAutoSendToggle" ${autoSendSettings.enabled ? 'checked' : ''}>
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="toggle-label">Enable Auto-Send</span>
                                ${autoSendSettings.enabled ? '<span class="auto-send-status enabled">✅ Enabled</span>' : '<span class="auto-send-status disabled">⏸️ Disabled</span>'}
                            </div>
                            ${autoSendSettings.failureCount > 0 ? `
                                <div class="auto-send-warning">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">${autoSendSettings.failureCount} recent failure(s). ${autoSendSettings.failureCount >= 3 ? 'Auto-send has been disabled.' : ''}</span>
                                </div>
                            ` : ''}
                            ${autoSendSettings.lastSent ? `
                                <div class="auto-send-info">
                                    <span class="info-icon">📤</span>
                                    <span class="info-text">Last sent: ${new Date(autoSendSettings.lastSent).toLocaleString()}</span>
                                </div>
                            ` : ''}
                        </div>
                        <div class="config-actions">
                            <button id="testDiscordConnection" class="btn btn-outline btn-sm">🔧 Test Connection</button>
                            <button id="editDiscordSettings" class="btn btn-outline btn-sm">✏️ Edit</button>
                            <button id="clearDiscordSettings" class="btn btn-danger btn-sm">🗑️ Clear</button>
                        </div>
                    </div>
                `;
            } else {
                // Show setup form
                discordContent.innerHTML = `
                    <div class="discord-setup">
                        <div class="setup-instructions">
                            <h5>📋 Setup Instructions</h5>
                            <ol>
                                <li>Go to your Discord server settings</li>
                                <li>Navigate to Integrations → Webhooks</li>
                                <li>Click "New Webhook" or "Create Webhook"</li>
                                <li>Choose the channel where you want to receive messages</li>
                                <li>Copy the webhook URL</li>
                            </ol>
                        </div>
                        <div class="setup-form">
                            <div class="form-group">
                                <label for="discordWebhookUrl">Webhook URL:</label>
                                <input type="password" id="discordWebhookUrl" placeholder="https://discord.com/api/webhooks/ID/TOKEN" class="form-input">
                                <small class="form-hint">Format: https://discord.com/api/webhooks/ID/TOKEN</small>
                            </div>
                            <div class="form-actions">
                                <button id="saveDiscordSettings" class="btn btn-primary">💾 Save & Test</button>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // Add event listeners for the dynamically created elements
            this.setupDiscordEventListeners();
            
        } catch (error) {
            console.error('Error loading Discord settings:', error);
            discordContent.innerHTML = `
                <div class="error-message">
                    <span class="error-icon">❌</span>
                    <span>Error loading Discord settings. Please try again.</span>
                </div>
            `;
        }
    }

    setupTelegramEventListeners() {
        // Save settings
        const saveBtn = document.getElementById('saveTelegramSettings');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveTelegramSettings();
            });
        }
        
        // Test connection
        const testBtn = document.getElementById('testTelegramConnection');
        if (testBtn) {
            testBtn.addEventListener('click', async () => {
                await this.testTelegramConnection();
            });
        }
        
        // Edit settings
        const editBtn = document.getElementById('editTelegramSettings');
        if (editBtn) {
            editBtn.addEventListener('click', async () => {
                await this.editTelegramSettings();
            });
        }
        
        // Clear settings
        const clearBtn = document.getElementById('clearTelegramSettings');
        if (clearBtn) {
            clearBtn.addEventListener('click', async () => {
                await this.clearTelegramSettings();
            });
        }
        
        // Auto-send toggle
        const autoSendToggle = document.getElementById('telegramAutoSendToggle');
        if (autoSendToggle) {
            autoSendToggle.addEventListener('change', async (e) => {
                await this.handleTelegramAutoSendToggle(e.target.checked);
            });
        }
    }

    async saveTelegramSettings() {
        const botToken = document.getElementById('telegramBotToken').value.trim();
        const chatId = document.getElementById('telegramChatId').value.trim();
        const saveBtn = document.getElementById('saveTelegramSettings');
        
        if (!botToken || !chatId) {
            this.showError('Please fill in both bot token and chat ID');
            return;
        }
        
        try {
            // Show saving state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '⏳ Saving...';
            
            // Save settings
            const result = await saveTelegramSettings(botToken, chatId);
            
            if (result.success) {
                // Test connection immediately
                const testResult = await testTelegramConnection(botToken, chatId);
                
                if (testResult.success) {
                    this.showSuccess('Telegram settings saved and tested successfully!');
                    // Reload settings content to show configured state
                    await this.loadSettingsContent();
                } else {
                    this.showError(`Settings saved but test failed: ${testResult.error}`);
                    // Still reload to show configured state
                    await this.loadSettingsContent();
                }
            } else {
                this.showError(result.error);
                saveBtn.disabled = false;
                saveBtn.innerHTML = '💾 Save & Test';
            }
            
        } catch (error) {
            console.error('Error saving Telegram settings:', error);
            this.showError('Failed to save Telegram settings');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '💾 Save & Test';
        }
    }

    async testTelegramConnection() {
        const testBtn = document.getElementById('testTelegramConnection');
        
        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '⏳ Testing...';
            
            const settings = await getTelegramSettings();
            if (!settings) {
                this.showError('No Telegram settings found');
                return;
            }
            
            const result = await testTelegramConnection(settings.botToken, settings.chatId);
            
            if (result.success) {
                this.showSuccess('Telegram connection test successful!');
            } else {
                this.showError(`Connection test failed: ${result.error}`);
            }
            
        } catch (error) {
            console.error('Error testing Telegram connection:', error);
            this.showError('Failed to test Telegram connection');
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '🔧 Test Connection';
        }
    }

    async editTelegramSettings() {
        // Switch back to setup form
        await this.loadSettingsContent();
    }

    async clearTelegramSettings() {
        if (!confirm('Are you sure you want to clear your Telegram settings? This cannot be undone.')) {
            return;
        }
        
        try {
            const success = await clearTelegramSettings();
            if (success) {
                this.showSuccess('Telegram settings cleared successfully');
                await this.loadSettingsContent();
            } else {
                this.showError('Failed to clear Telegram settings');
            }
        } catch (error) {
            console.error('Error clearing Telegram settings:', error);
            this.showError('Failed to clear Telegram settings');
        }
    }

    setupDiscordEventListeners() {
        // Save settings
        const saveBtn = document.getElementById('saveDiscordSettings');
        if (saveBtn) {
            saveBtn.addEventListener('click', async () => {
                await this.saveDiscordSettings();
            });
        }
        
        // Test connection
        const testBtn = document.getElementById('testDiscordConnection');
        if (testBtn) {
            testBtn.addEventListener('click', async () => {
                await this.testDiscordConnection();
            });
        }
        
        // Edit settings
        const editBtn = document.getElementById('editDiscordSettings');
        if (editBtn) {
            editBtn.addEventListener('click', async () => {
                await this.editDiscordSettings();
            });
        }
        
        // Clear settings
        const clearBtn = document.getElementById('clearDiscordSettings');
        if (clearBtn) {
            clearBtn.addEventListener('click', async () => {
                await this.clearDiscordSettings();
            });
        }
        
        // Auto-send toggle
        const autoSendToggle = document.getElementById('discordAutoSendToggle');
        if (autoSendToggle) {
            autoSendToggle.addEventListener('change', async (e) => {
                await this.handleDiscordAutoSendToggle(e.target.checked);
            });
        }
    }

    async saveDiscordSettings() {
        const webhookUrl = document.getElementById('discordWebhookUrl').value.trim();
        const saveBtn = document.getElementById('saveDiscordSettings');
        
        if (!webhookUrl) {
            this.showError('Please enter a webhook URL');
            return;
        }
        
        try {
            // Show saving state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '⏳ Saving...';
            
            // Save settings
            const result = await saveDiscordSettings(webhookUrl);
            
            if (result.success) {
                // Test connection immediately
                const testResult = await testDiscordWebhook(webhookUrl);
                
                if (testResult.success) {
                    this.showSuccess('Discord settings saved and tested successfully!');
                    // Reload settings content to show configured state
                    await this.loadSettingsContent();
                } else {
                    this.showError(`Settings saved but test failed: ${testResult.error}`);
                    // Still reload to show configured state
                    await this.loadSettingsContent();
                }
            } else {
                this.showError(result.error);
                saveBtn.disabled = false;
                saveBtn.innerHTML = '💾 Save & Test';
            }
            
        } catch (error) {
            console.error('Error saving Discord settings:', error);
            this.showError('Failed to save Discord settings');
            saveBtn.disabled = false;
            saveBtn.innerHTML = '💾 Save & Test';
        }
    }

    async testDiscordConnection() {
        const testBtn = document.getElementById('testDiscordConnection');
        
        try {
            testBtn.disabled = true;
            testBtn.innerHTML = '⏳ Testing...';
            
            const settings = await getDiscordSettings();
            if (!settings) {
                this.showError('No Discord settings found');
                return;
            }
            
            const result = await testDiscordWebhook(settings.webhookUrl);
            
            if (result.success) {
                this.showSuccess('Discord connection test successful!');
            } else {
                this.showError(`Connection test failed: ${result.error}`);
            }
            
        } catch (error) {
            console.error('Error testing Discord connection:', error);
            this.showError('Failed to test Discord connection');
        } finally {
            testBtn.disabled = false;
            testBtn.innerHTML = '🔧 Test Connection';
        }
    }

    async editDiscordSettings() {
        // Switch back to setup form
        await this.loadSettingsContent();
    }

    async clearDiscordSettings() {
        if (!confirm('Are you sure you want to clear your Discord settings? This cannot be undone.')) {
            return;
        }
        
        try {
            const success = await clearDiscordSettings();
            if (success) {
                this.showSuccess('Discord settings cleared successfully');
                await this.loadSettingsContent();
            } else {
                this.showError('Failed to clear Discord settings');
            }
        } catch (error) {
            console.error('Error clearing Discord settings:', error);
            this.showError('Failed to clear Discord settings');
        }
    }

    async handleTelegramAutoSendToggle(enabled) {
        try {
            const result = await saveTelegramAutoSendSettings(enabled);
            
            if (result.success) {
                this.showSuccess(result.message);
                // Reload settings to update UI with new state
                await this.loadTelegramSettings();
            } else {
                this.showError(result.error);
                // Reload settings to revert toggle state
                await this.loadTelegramSettings();
            }
        } catch (error) {
            console.error('Error toggling Telegram auto-send:', error);
            this.showError('Failed to update auto-send settings');
            // Reload settings to revert toggle state
            await this.loadTelegramSettings();
        }
    }

    async handleDiscordAutoSendToggle(enabled) {
        try {
            const result = await saveDiscordAutoSendSettings(enabled);
            
            if (result.success) {
                this.showSuccess(result.message);
                // Reload settings to update UI with new state
                await this.loadDiscordSettings();
            } else {
                this.showError(result.error);
                // Reload settings to revert toggle state
                await this.loadDiscordSettings();
            }
        } catch (error) {
            console.error('Error toggling Discord auto-send:', error);
            this.showError('Failed to update auto-send settings');
            // Reload settings to revert toggle state
            await this.loadDiscordSettings();
        }
    }

    showHelp() {
        this.showSection('helpSection');
    }

    showAbout() {
        this.showSection('aboutSection');
    }

    // Analysis History Management
    async checkPendingAnalysis() {
        try {
            const result = await chrome.storage.local.get(['pendingAnalysis']);
            if (result.pendingAnalysis) {
                const { type, data, timestamp } = result.pendingAnalysis;
                
                // Only process if it's recent (within 10 seconds)
                if (Date.now() - timestamp < 10000) {
                    console.log('Found pending analysis:', type, data.substring(0, 50) + '...');
                    
                    // Clear the pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);
                    
                    // Trigger the appropriate analysis
                    if (type === 'selection' && data) {
                        await this.analyzeSelectionWithText(data);
                    }
                } else {
                    // Clear old pending analysis
                    await chrome.storage.local.remove(['pendingAnalysis']);
                }
            }
        } catch (error) {
            console.error('Error checking pending analysis:', error);
        }
    }

    // Check for context menu actions
    async checkContextMenuActions() {
        try {
            const result = await chrome.storage.local.get([
                'showUpgradeSection', 
                'showCustomAnalysis', 
                'showHistorySection',
                'contextMenuData',
                'upgradeReason',
                'contextMenuTrigger'
            ]);
            
            // Handle upgrade section request
            if (result.showUpgradeSection) {
                await chrome.storage.local.remove(['showUpgradeSection', 'upgradeReason']);
                this.showSection('upgradeSection');
                
                // Show specific message if from context menu
                if (result.upgradeReason) {
                    this.showError(result.upgradeReason + ' - Upgrade to Pro to access this feature');
                }
                return;
            }
            
            // Handle custom analysis request
            if (result.showCustomAnalysis && result.contextMenuData) {
                await chrome.storage.local.remove(['showCustomAnalysis', 'contextMenuData']);
                
                // Pre-fill custom analysis form if there's selected text
                if (result.contextMenuData.hasSelection) {
                    document.getElementById('customPrompt').value = `Analyze the following text:\n\n${result.contextMenuData.selectedText}`;
                    // Select "Selected Text" radio button
                    document.querySelector('input[name="dataSource"][value="selection"]').checked = true;
                } else {
                    // Select "Full Page" radio button
                    document.querySelector('input[name="dataSource"][value="page"]').checked = true;
                }
                
                this.showSection('customForm');
                return;
            }
            
            // Handle history section request
            if (result.showHistorySection) {
                await chrome.storage.local.remove(['showHistorySection']);
                this.showSection('analysisHistorySection');
                this.loadAndDisplayAnalysis();
                return;
            }
            
            // Handle context menu triggers (immediate analysis requests)
            if (result.contextMenuTrigger) {
                await chrome.storage.local.remove(['contextMenuTrigger']);
                
                // Check if the trigger is recent (within 10 seconds)
                const triggerTime = result.contextMenuTrigger.timestamp;
                const timeDiff = Date.now() - triggerTime;
                
                if (timeDiff < 10000) { // 10 seconds
                    console.log('Processing context menu trigger:', result.contextMenuTrigger.type);
                    
                    // Show loading section immediately for user feedback
                    this.showSection('loadingSection');
                    
                    if (result.contextMenuTrigger.type === 'selection') {
                        // Trigger text analysis with loading animation
                        await this.analyzeSelectionWithText(result.contextMenuTrigger.data);
                    } else if (result.contextMenuTrigger.type === 'page') {
                        // Trigger page analysis with loading animation
                        await this.analyzePageWithData(result.contextMenuTrigger.data);
                    }
                    return;
                }
            }
            
        } catch (error) {
            console.error('Error checking context menu actions:', error);
        }
    }

    // Setup message listeners for communication with content script
    setupMessageListeners() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'triggerAnalysis') {
                console.log('Received triggerAnalysis message:', request.type);
                
                if (request.type === 'selection' && request.data) {
                    this.analyzeSelectionWithText(request.data);
                }
                
                sendResponse({ success: true });
            }
        });
    }

    // Setup event delegation for dynamically created elements
    setupEventDelegation() {
        // Event delegation for history items
        document.addEventListener('click', (e) => {
            const historyItem = e.target.closest('.history-item');
            const deleteBtn = e.target.closest('.delete-btn');
            const telegramBtn = e.target.closest('.telegram-send-btn');
            const discordBtn = e.target.closest('.discord-send-btn');
            const viewDetailsLink = e.target.closest('.view-details-link');
            
            if (deleteBtn && historyItem) {
                e.stopPropagation();
                const analysisId = deleteBtn.dataset.analysisId;
                if (analysisId) {
                    this.deleteAnalysis(analysisId);
                }
            } else if (telegramBtn && historyItem) {
                e.stopPropagation();
                const analysisId = telegramBtn.dataset.analysisId;
                if (analysisId) {
                    this.sendAnalysisToTelegram(analysisId);
                }
            } else if (discordBtn && historyItem) {
                e.stopPropagation();
                const analysisId = discordBtn.dataset.analysisId;
                if (analysisId) {
                    this.sendAnalysisToDiscord(analysisId);
                }
            } else if (viewDetailsLink && historyItem) {
                e.stopPropagation();
                const analysisId = historyItem.dataset.analysisId;
                if (analysisId) {
                    this.viewAnalysisFromHistory(analysisId);
                }
            } else if (historyItem && !deleteBtn && !telegramBtn && !discordBtn) {
                const analysisId = historyItem.dataset.analysisId;
                if (analysisId) {
                    this.viewAnalysisFromHistory(analysisId);
                }
            }
        });

        // Event delegation for prompt card buttons
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.btn-icon[data-action]');
            if (button) {
                e.stopPropagation();
                const action = button.dataset.action;
                const promptId = button.dataset.promptId;
                
                if (promptId) {
                    switch (action) {
                        case 'use':
                            this.usePrompt(promptId);
                            break;
                        case 'copy':
                            this.copyPromptContent(promptId);
                            break;
                        case 'pin':
                            this.togglePromptPin(promptId);
                            break;
                        case 'edit':
                            this.editPrompt(promptId);
                            break;
                        case 'delete':
                            this.deletePromptConfirm(promptId);
                            break;
                    }
                }
            }
        });

        // Event delegation for tag clicks
        document.addEventListener('click', (e) => {
            const tag = e.target.closest('.tag[data-tag]');
            if (tag) {
                e.stopPropagation();
                const tagName = tag.dataset.tag;
                this.filterByTag(tagName);
            }
        });

        // Event delegation for tag filter buttons
        document.addEventListener('click', (e) => {
            const filterButton = e.target.closest('.tag-filter[data-filter-tag]');
            if (filterButton) {
                e.stopPropagation();
                const tagName = filterButton.dataset.filterTag || null;
                this.filterByTag(tagName);
            }
        });

        // Enhanced keyboard navigation for prompt cards and tags
        document.addEventListener('keydown', (e) => {
            const tag = e.target.closest('.tag');
            
            if (tag && (e.key === 'Enter' || e.key === ' ')) {
                e.preventDefault();
                if (tag.dataset.tag) {
                    this.filterByTag(tag.dataset.tag);
                }
            }
        });

        // Event delegation for pagination controls
        document.addEventListener('click', (e) => {
            const paginationBtn = e.target.closest('.pagination-btn[data-page]');
            if (paginationBtn && !paginationBtn.disabled) {
                e.stopPropagation();
                const page = parseInt(paginationBtn.dataset.page);
                const paginationContainer = paginationBtn.closest('.pagination-wrapper');
                
                if (paginationContainer) {
                    // Determine which pagination this belongs to
                    if (paginationContainer.id === 'historyPagination') {
                        this.historyPagination.goToPage(page);
                    } else if (paginationContainer.id === 'promptPagination') {
                        this.promptPagination.goToPage(page);
                    }
                }
            }
        });
    }

    // New method to analyze selection with provided text
    async analyzeSelectionWithText(selectedText) {
        try {
            if (!selectedText || selectedText.trim().length === 0) {
                this.showError('No text provided for analysis.');
                return;
            }

            this.showSection('loadingSection');

            const prompt = `Please analyze the following selected text and provide insights, key points, and any relevant analysis:

Selected Text:
${selectedText}

Please provide a comprehensive analysis including:
1. Summary of the content
2. Key insights and takeaways
3. Any important details or patterns
4. Recommendations or next steps if applicable`;

            await this.performAnalysis(prompt, 'Text Selection Analysis');
            
        } catch (error) {
            console.error('Error analyzing provided text:', error);
            this.showError('Failed to analyze selected text');
            this.showSection('actionsSection');
        }
    }

    // New method to analyze page with provided data
    async analyzePageWithData(pageData) {
        try {
            if (!pageData || !pageData.content) {
                this.showError('No page content available for analysis.');
                return;
            }

            this.showSection('loadingSection');

            const prompt = `Please analyze the following webpage and provide comprehensive insights:

Page Title: ${pageData.title}
URL: ${pageData.url}

Page Content:
${pageData.content}

Please provide a detailed analysis including:
1. Summary of the page content and purpose
2. Key topics and themes discussed
3. Important information and insights
4. Content quality and credibility assessment
5. Any actionable takeaways or recommendations`;

            await this.performAnalysis(prompt, 'Full Page Analysis');
            
        } catch (error) {
            console.error('Error analyzing page:', error);
            this.showError('Failed to analyze page');
            this.showSection('actionsSection');
        }
    }

    async loadAndDisplayAnalysis() {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const allHistory = data.hustleplugAnalysis || [];
            const historyList = document.getElementById('analysisHistoryList');
            const paginationContainer = document.getElementById('historyPagination');

            if (allHistory.length === 0) {
                historyList.innerHTML = '<p style="text-align: center; color: #888;">No analysis history found.</p>';
                paginationContainer.innerHTML = '';
                return;
            }

            // Check if user is Pro and has integrations configured
            const proStatus = await checkProStatus();
            const telegramConfigured = proStatus.isPro ? await isTelegramConfigured() : false;
            const discordConfigured = proStatus.isPro ? await isDiscordConfigured() : false;

            // Sort history by date (newest first)
            const sortedHistory = [...allHistory].sort((a, b) => new Date(b.date) - new Date(a.date));
            
            // Get paginated data
            const { data: paginatedHistory, pagination } = this.historyPagination.getPaginatedData(sortedHistory);

            // Clear previous content
            historyList.innerHTML = '';
            
            // Display paginated items
            paginatedHistory.forEach(item => {
                const itemEl = document.createElement('div');
                itemEl.className = 'history-item';
                itemEl.dataset.analysisId = item.id; // Add ID to dataset

                // Handle both string and object based results for backward compatibility
                const resultContent = typeof item.result === 'object' && item.result !== null && item.result.content ? item.result.content : item.result;

                // Build integration buttons HTML if configured
                const telegramButtonHtml = telegramConfigured ? `
                    <button class="telegram-send-btn" data-analysis-id="${item.id}" title="Send to Telegram">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 3L3 10.5L10.5 13.5L13.5 21L21 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M13.5 13.5L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                ` : '';

                const discordButtonHtml = discordConfigured ? `
                    <button class="discord-send-btn" data-analysis-id="${item.id}" title="Send to Discord">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                        </svg>
                    </button>
                ` : '';

                itemEl.innerHTML = `
                    <div class="history-item-header">
                        <span class="history-item-title">${item.analysisType}</span>
                        <div class="history-item-meta">
                            <span class="history-item-date">${new Date(item.date).toLocaleString()}</span>
                            <div class="history-item-actions">
                                ${telegramButtonHtml}
                                ${discordButtonHtml}
                                <button class="delete-btn" data-analysis-id="${item.id}" title="Delete analysis">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M10 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M14 11V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="history-item-summary">
                        ${this.formatAnalysisContent(resultContent, true)}
                    </div>
                    <div class="history-item-footer">
                        <span class="view-details-link">View Details &rarr;</span>
                    </div>
                `;
                historyList.appendChild(itemEl);
            });

            // Update pagination controls
            paginationContainer.innerHTML = this.historyPagination.generatePaginationHTML(pagination);

        } catch (error) {
            console.error('Error loading analysis history:', error);
            const historyList = document.getElementById('analysisHistoryList');
            const paginationContainer = document.getElementById('historyPagination');
            historyList.innerHTML = '<p style="text-align: center; color: #d9534f;">Error loading history.</p>';
            paginationContainer.innerHTML = '';
        }
    }

    async saveAnalysis(analysisType, result) {
        try {
            const newEntry = {
                id: `analysis-${Date.now()}`,
                date: new Date().toISOString(),
                analysisType,
                result
            };

            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            history.push(newEntry);

            await chrome.storage.local.set({ hustleplugAnalysis: history });
        } catch (error) {
            console.error('Error saving analysis:', error);
        }
    }

    // This is a helper for development to test the history view.
    async addDummyHistory() {
        const dummyHistory = [
            {
                id: `analysis-${Date.now() - 10000}`,
                date: new Date(Date.now() - 10000).toISOString(),
                analysisType: 'Sample: Page Analysis',
                result: "This is a dummy summary of a full page analysis. It highlights key findings and provides a concise overview. **This part is bold.**\nAnd this is a new line."
            },
            {
                id: `analysis-${Date.now() - 20000}`,
                date: new Date(Date.now() - 20000).toISOString(),
                analysisType: 'Sample: Selection Analysis',
                result: "This is a short summary for a selected piece of text. It would normally contain insights about the selection. Here are some points:\n1. First point\n2. Second point"
            }
        ];
        await chrome.storage.local.set({ hustleplugAnalysis: dummyHistory });
        this.showSuccess('Dummy history created!');
        this.loadAndDisplayAnalysis();
    }

    // New method to view a specific analysis from history
    async viewAnalysisFromHistory(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (analysisItem) {
                this.displayResults(analysisItem.result, analysisItem.analysisType, new Date(analysisItem.date));
                this.showSection('resultsSection');
            } else {
                this.showError('Could not find the selected analysis.');
            }
        } catch (error) {
            console.error('Error viewing analysis from history:', error);
            this.showError('Failed to load the analysis.');
        }
    }

    // Delete analysis from history
    async deleteAnalysis(analysisId) {
        try {
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            
            // Filter out the analysis to delete
            const updatedHistory = history.filter(item => item.id !== analysisId);
            
            // Update storage
            await chrome.storage.local.set({ hustleplugAnalysis: updatedHistory });
            
            // Check if current page is now empty and adjust if needed
            const currentPageData = this.historyPagination.getPaginatedData(updatedHistory);
            if (currentPageData.data.length === 0 && this.historyPagination.currentPage > 1) {
                this.historyPagination.currentPage = Math.max(1, this.historyPagination.currentPage - 1);
            }
            
            // Refresh the display
            await this.loadAndDisplayAnalysis();
            
            // Show success message
            this.showSuccess('Analysis deleted successfully');
            
        } catch (error) {
            console.error('Error deleting analysis:', error);
            this.showError('Failed to delete analysis');
        }
    }

    // ============= PROMPT MANAGEMENT METHODS =============

    async loadPromptManagement() {
        await this.refreshPromptList();
        await this.loadSavedPromptsSelect();
        await this.loadTagFilters();
    }

    async loadSavedPromptsSelect() {
        try {
            const prompts = await promptManager.getPrompts();
            const select = document.getElementById('savedPromptsSelect');
            
            // Clear existing options except the first one
            select.innerHTML = '<option value="">Choose a saved prompt or create new...</option>';
            
            // Add pinned prompts first
            const pinnedPrompts = prompts.filter(p => p.isPinned);
            if (pinnedPrompts.length > 0) {
                const pinnedGroup = document.createElement('optgroup');
                pinnedGroup.label = '📌 Pinned';
                pinnedPrompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = prompt.title;
                    pinnedGroup.appendChild(option);
                });
                select.appendChild(pinnedGroup);
            }
            
            // Add other prompts
            const otherPrompts = prompts.filter(p => !p.isPinned);
            if (otherPrompts.length > 0) {
                const otherGroup = document.createElement('optgroup');
                otherGroup.label = '📄 All Prompts';
                otherPrompts.forEach(prompt => {
                    const option = document.createElement('option');
                    option.value = prompt.id;
                    option.textContent = prompt.title;
                    otherGroup.appendChild(option);
                });
                select.appendChild(otherGroup);
            }
        } catch (error) {
            console.error('Error loading saved prompts select:', error);
        }
    }

    async loadSelectedPrompt() {
        const select = document.getElementById('savedPromptsSelect');
        const promptId = select.value;
        
        if (!promptId) {
            this.showError('Please select a prompt to load');
            return;
        }

        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                document.getElementById('customPrompt').value = prompt.content;
                await promptManager.incrementUsage(promptId);
                this.showSuccess(`Loaded: ${prompt.title}`);
            } else {
                this.showError('Prompt not found');
            }
        } catch (error) {
            console.error('Error loading selected prompt:', error);
            this.showError('Failed to load prompt');
        }
    }

    async saveCurrentPrompt() {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.showError('Unable to verify Pro status. Please try again.');
            return;
        }

        const content = document.getElementById('customPrompt').value.trim();
        
        if (!content) {
            this.showError('Please enter a prompt to save');
            return;
        }

        // Pre-fill the modal with current content
        document.getElementById('promptContent').value = content;
        document.getElementById('promptTitle').value = '';
        document.getElementById('promptTags').value = '';
        document.getElementById('promptPinned').checked = false;
        
        this.currentEditingPrompt = null;
        document.getElementById('promptEditorTitle').textContent = 'Save Current Prompt';
        document.getElementById('promptEditorModal').style.display = 'flex';
    }

    async refreshPromptList() {
        try {
            let allPrompts = await promptManager.getPrompts();
            const sortBy = document.getElementById('promptSort').value;
            allPrompts = promptManager.sortPrompts(allPrompts, sortBy);
            
            // Apply current filter
            if (this.currentFilterTag) {
                allPrompts = allPrompts.filter(p => p.tags.includes(this.currentFilterTag));
            }

            // Get paginated data
            const { data: paginatedPrompts, pagination } = this.promptPagination.getPaginatedData(allPrompts);

            this.displayPromptList(paginatedPrompts, pagination);
        } catch (error) {
            console.error('Error refreshing prompt list:', error);
        }
    }

    displayPromptList(prompts, pagination = null) {
        const listContainer = document.getElementById('promptList');
        const paginationContainer = document.getElementById('promptPagination');
        
        if (prompts.length === 0) {
            listContainer.innerHTML = '<div class="no-prompts">No prompts found. Create your first prompt!</div>';
            if (paginationContainer) {
                paginationContainer.innerHTML = '';
            }
            return;
        }

        listContainer.innerHTML = prompts.map(prompt => `
            <div class="prompt-item" data-prompt-id="${prompt.id}">
                <div class="prompt-header">
                    <div class="prompt-title">
                        ${prompt.isPinned ? '📌 ' : ''}${this.escapeHtml(prompt.title)}
                        <span class="usage-count">${prompt.usageCount || 0} uses</span>
                    </div>
                    <div class="prompt-actions">
                        <button class="btn-icon primary" data-action="use" data-prompt-id="${prompt.id}" title="Use this prompt" aria-label="Use prompt: ${this.escapeHtml(prompt.title)}">
                            🚀
                        </button>
                        <button class="btn-icon copy" data-action="copy" data-prompt-id="${prompt.id}" title="Copy prompt to clipboard" aria-label="Copy prompt content">
                            📋
                        </button>
                        <button class="btn-icon pin" data-action="pin" data-prompt-id="${prompt.id}" title="${prompt.isPinned ? 'Unpin' : 'Pin'} prompt" aria-label="${prompt.isPinned ? 'Unpin' : 'Pin'} prompt">
                            ${prompt.isPinned ? '📌' : '📍'}
                        </button>
                        <button class="btn-icon edit" data-action="edit" data-prompt-id="${prompt.id}" title="Edit prompt" aria-label="Edit prompt: ${this.escapeHtml(prompt.title)}">
                            ✏️
                        </button>
                        <button class="btn-icon delete" data-action="delete" data-prompt-id="${prompt.id}" title="Delete prompt" aria-label="Delete prompt: ${this.escapeHtml(prompt.title)}">
                            🗑️
                        </button>
                    </div>
                </div>
                <div class="prompt-content" title="${this.escapeHtml(prompt.content)}" data-full-content="${this.escapeHtml(prompt.content)}">
                    ${this.escapeHtml(prompt.content.substring(0, 150))}${prompt.content.length > 150 ? '...' : ''}
                </div>
                <div class="prompt-footer">
                    <div class="prompt-tags">
                        ${prompt.tags.map(tag => `<span class="tag" data-tag="${this.escapeHtml(tag)}" role="button" tabindex="0" aria-label="Filter by tag: ${this.escapeHtml(tag)}">${this.escapeHtml(tag)}</span>`).join('')}
                    </div>
                    <div class="prompt-date">
                        Updated: ${new Date(prompt.updatedAt).toLocaleDateString()}
                    </div>
                </div>
            </div>
        `).join('');

        // Update pagination controls if pagination data is provided
        if (pagination && paginationContainer) {
            paginationContainer.innerHTML = this.promptPagination.generatePaginationHTML(pagination);
        }
    }

    async searchPrompts(query) {
        try {
            let allPrompts = await promptManager.searchPrompts(query);
            const sortBy = document.getElementById('promptSort').value;
            allPrompts = promptManager.sortPrompts(allPrompts, sortBy);
            
            // Apply current filter
            if (this.currentFilterTag) {
                allPrompts = allPrompts.filter(p => p.tags.includes(this.currentFilterTag));
            }

            // Reset to first page when searching
            this.promptPagination.resetToFirstPage();
            
            // Get paginated data
            const { data: paginatedPrompts, pagination } = this.promptPagination.getPaginatedData(allPrompts);

            this.displayPromptList(paginatedPrompts, pagination);
        } catch (error) {
            console.error('Error searching prompts:', error);
        }
    }

    async sortPrompts(sortBy) {
        await this.refreshPromptList();
    }

    async loadTagFilters() {
        try {
            const tags = await promptManager.getAllTags();
            const filterContainer = document.getElementById('filterTags');
            
            if (tags.length === 0) {
                filterContainer.innerHTML = '';
                return;
            }

            filterContainer.innerHTML = `
                <div class="tag-filters">
                    <button class="tag-filter ${!this.currentFilterTag ? 'active' : ''}" data-filter-tag="">
                        All
                    </button>
                    ${tags.map(tag => `
                        <button class="tag-filter ${this.currentFilterTag === tag ? 'active' : ''}" data-filter-tag="${this.escapeHtml(tag)}">
                            ${this.escapeHtml(tag)}
                        </button>
                    `).join('')}
                </div>
            `;
        } catch (error) {
            console.error('Error loading tag filters:', error);
        }
    }

    async filterByTag(tag) {
        this.currentFilterTag = tag;
        // Reset to first page when filtering
        this.promptPagination.resetToFirstPage();
        await this.refreshPromptList();
        await this.loadTagFilters();
    }

    async usePrompt(promptId) {
        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                document.getElementById('customPrompt').value = prompt.content;
                await promptManager.incrementUsage(promptId);
                this.showSection('customForm');
                this.showSuccess(`Loaded: ${prompt.title}`);
                await this.loadSavedPromptsSelect(); // Refresh the select dropdown
            }
        } catch (error) {
            console.error('Error using prompt:', error);
            this.showError('Failed to load prompt');
        }
    }

    async togglePromptPin(promptId) {
        try {
            await promptManager.togglePin(promptId);
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            this.showSuccess('Prompt pin status updated');
        } catch (error) {
            console.error('Error toggling pin:', error);
            this.showError('Failed to update pin status');
        }
    }

    async openPromptEditor(prompt = null) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.showError('Unable to verify Pro status. Please try again.');
            return;
        }

        this.currentEditingPrompt = prompt;
        
        if (prompt) {
            document.getElementById('promptTitle').value = prompt.title;
            document.getElementById('promptContent').value = prompt.content;
            document.getElementById('promptTags').value = prompt.tags.join(', ');
            document.getElementById('promptPinned').checked = prompt.isPinned;
            document.getElementById('promptEditorTitle').textContent = 'Edit Prompt';
        } else {
            document.getElementById('promptTitle').value = '';
            document.getElementById('promptContent').value = '';
            document.getElementById('promptTags').value = '';
            document.getElementById('promptPinned').checked = false;
            document.getElementById('promptEditorTitle').textContent = 'New Prompt';
        }
        
        document.getElementById('promptEditorModal').style.display = 'flex';
    }

    async editPrompt(promptId) {
        try {
            const prompts = await promptManager.getPrompts();
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                await this.openPromptEditor(prompt);
            }
        } catch (error) {
            console.error('Error loading prompt for edit:', error);
            this.showError('Failed to load prompt for editing');
        }
    }

    closePromptEditor() {
        document.getElementById('promptEditorModal').style.display = 'none';
        this.currentEditingPrompt = null;
    }

    async showKeyManagementModal() {
        const modal = document.getElementById('keyManagementModal');
        const content = document.getElementById('keyManagementContent');
        
        try {
            // Get current key info
            const maskedKey = await getMaskedProKey();
            const detailedStatus = await getDetailedProStatus();
            
            content.innerHTML = `
                <div class="key-management-content">
                    <div class="current-key-section">
                        <h4>Current Pro Key</h4>
                        <div class="current-key-display">
                            <div class="key-info">
                                <label>Key:</label>
                                <span class="masked-key">${maskedKey || 'No key configured'}</span>
                            </div>
                            ${detailedStatus.isPro ? `
                                <div class="key-status-info">
                                    <span class="status-badge active">✅ Valid</span>
                                    ${detailedStatus.membershipDetails ? `
                                        <span class="key-expires">Expires: ${new Date(detailedStatus.membershipDetails.expiresAt).toLocaleDateString()}</span>
                                    ` : ''}
                                </div>
                            ` : `
                                <div class="key-status-info">
                                    <span class="status-badge invalid">❌ ${detailedStatus.expired ? 'Expired' : 'Invalid'}</span>
                                </div>
                            `}
                        </div>
                    </div>
                    
                    <div class="key-actions-section">
                        <h4>Key Management</h4>
                        <div class="key-action-buttons">
                            <button id="changeProKey" class="btn btn-primary">
                                🔄 Change Pro Key
                            </button>
                            <button id="removeProKey" class="btn btn-danger">
                                🗑️ Remove Key
                            </button>
                            <button id="testCurrentKey" class="btn btn-outline">
                                🔧 Test Current Key
                            </button>
                        </div>
                    </div>
                    
                    <div id="keyChangeForm" class="key-change-form" style="display: none;">
                        <h4>Enter New Pro Key</h4>
                        <div class="form-group">
                            <label for="newProKeyInput">New Pro Key:</label>
                            <input type="password" id="newProKeyInput" placeholder="Enter your new pro key..." class="form-input">
                            <div class="key-status" id="newKeyStatus">
                                <span class="status-indicator"></span>
                                <span class="status-text">Enter your new pro key to validate</span>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button id="validateNewKey" class="btn btn-primary">
                                🔑 Validate & Save
                            </button>
                            <button id="cancelKeyChange" class="btn btn-secondary">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            // Add event listeners for the modal content
            this.setupKeyManagementEventListeners();
            
            modal.style.display = 'flex';
            
        } catch (error) {
            console.error('Error loading key management modal:', error);
            content.innerHTML = `
                <div class="error-message">
                    <span class="error-icon">❌</span>
                    <span>Error loading key management. Please try again.</span>
                </div>
            `;
            modal.style.display = 'flex';
        }
    }

    setupKeyManagementEventListeners() {
        // Change Pro Key button
        const changeKeyBtn = document.getElementById('changeProKey');
        if (changeKeyBtn) {
            changeKeyBtn.addEventListener('click', () => {
                document.getElementById('keyChangeForm').style.display = 'block';
                document.getElementById('newProKeyInput').focus();
            });
        }
        
        // Remove Pro Key button
        const removeKeyBtn = document.getElementById('removeProKey');
        if (removeKeyBtn) {
            removeKeyBtn.addEventListener('click', async () => {
                if (confirm('Are you sure you want to remove your Pro key? This will disable all Pro features.')) {
                    const success = await removeProKey();
                    if (success) {
                        this.showSuccess('Pro key removed successfully');
                        this.closeKeyManagementModal();
                        // Refresh the settings to update membership info
                        this.loadMembershipInfo();
                    } else {
                        this.showError('Failed to remove Pro key');
                    }
                }
            });
        }
        
        // Test Current Key button
        const testKeyBtn = document.getElementById('testCurrentKey');
        if (testKeyBtn) {
            testKeyBtn.addEventListener('click', async () => {
                testKeyBtn.disabled = true;
                testKeyBtn.textContent = '🔄 Testing...';
                
                try {
                    const status = await refreshProStatus();
                    if (status.isPro) {
                        this.showSuccess('Pro key is valid and working!');
                    } else {
                        this.showError('Pro key test failed: ' + status.message);
                    }
                } catch (error) {
                    this.showError('Failed to test Pro key');
                } finally {
                    testKeyBtn.disabled = false;
                    testKeyBtn.textContent = '🔧 Test Current Key';
                }
            });
        }
        
        // Validate New Key button
        const validateNewBtn = document.getElementById('validateNewKey');
        if (validateNewBtn) {
            validateNewBtn.addEventListener('click', async () => {
                await this.handleNewKeyValidation();
            });
        }
        
        // Cancel Key Change button
        const cancelBtn = document.getElementById('cancelKeyChange');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                document.getElementById('keyChangeForm').style.display = 'none';
                document.getElementById('newProKeyInput').value = '';
                this.updateNewKeyStatus('default', 'Enter your new pro key to validate');
            });
        }
        
        // Allow Enter key to validate new key
        const newKeyInput = document.getElementById('newProKeyInput');
        if (newKeyInput) {
            newKeyInput.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    await this.handleNewKeyValidation();
                }
            });
        }
    }

    async handleNewKeyValidation() {
        const newKeyInput = document.getElementById('newProKeyInput');
        const validateBtn = document.getElementById('validateNewKey');
        
        const newKey = newKeyInput.value.trim();
        
        if (!newKey) {
            this.updateNewKeyStatus('invalid', 'Please enter a pro key');
            return;
        }
        
        try {
            // Show validating state
            this.updateNewKeyStatus('validating', 'Validating new pro key...');
            validateBtn.disabled = true;
            validateBtn.innerHTML = '⏳ Validating...';
            
            // Validate the new key
            const result = await setProKey(newKey);
            
            if (result.success && result.isPro) {
                // Success - key is valid
                this.updateNewKeyStatus('valid', 'New pro key validated successfully!');
                
                // Get detailed info for success message
                const detailedStatus = await getDetailedProStatus();
                let successMessage = 'Pro key updated successfully!';
                
                if (detailedStatus.membershipDetails) {
                    const details = detailedStatus.membershipDetails;
                    if (details.daysRemaining > 0) {
                        successMessage += ` (${details.daysRemaining} days remaining)`;
                    }
                }
                
                // Show success and close modal
                setTimeout(() => {
                    this.showSuccess(successMessage);
                    this.closeKeyManagementModal();
                    // Refresh the settings to update membership info
                    this.loadMembershipInfo();
                }, 1000);
                
            } else {
                // Invalid key
                this.updateNewKeyStatus('invalid', result.message || 'Invalid pro key');
            }
            
        } catch (error) {
            console.error('Error validating new pro key:', error);
            this.updateNewKeyStatus('invalid', 'Validation failed. Please try again.');
        } finally {
            // Reset button state
            validateBtn.disabled = false;
            validateBtn.innerHTML = '🔑 Validate & Save';
        }
    }

    updateNewKeyStatus(status, message) {
        const newKeyStatus = document.getElementById('newKeyStatus');
        const statusText = newKeyStatus.querySelector('.status-text');
        
        // Remove existing status classes
        newKeyStatus.classList.remove('validating', 'valid', 'invalid');
        
        // Add new status class
        if (status !== 'default') {
            newKeyStatus.classList.add(status);
        }
        
        // Update message
        statusText.textContent = message;
    }

    closeKeyManagementModal() {
        const modal = document.getElementById('keyManagementModal');
        modal.style.display = 'none';
        
        // Reset form
        const keyChangeForm = document.getElementById('keyChangeForm');
        if (keyChangeForm) {
            keyChangeForm.style.display = 'none';
        }
        
        const newKeyInput = document.getElementById('newProKeyInput');
        if (newKeyInput) {
            newKeyInput.value = '';
        }
        
        // Reset status
        const newKeyStatus = document.getElementById('newKeyStatus');
        if (newKeyStatus) {
            newKeyStatus.classList.remove('validating', 'valid', 'invalid');
            const statusText = newKeyStatus.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = 'Enter your new pro key to validate';
            }
        }
    }

    async savePromptEdit() {
        const title = document.getElementById('promptTitle').value.trim();
        const content = document.getElementById('promptContent').value.trim();
        const tagsInput = document.getElementById('promptTags').value.trim();
        const isPinned = document.getElementById('promptPinned').checked;
        
        if (!title) {
            this.showError('Please enter a title for the prompt');
            return;
        }
        
        if (!content) {
            this.showError('Please enter content for the prompt');
            return;
        }

        const tags = tagsInput ? tagsInput.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
        
        const promptData = {
            title,
            content,
            tags,
            isPinned
        };

        if (this.currentEditingPrompt) {
            promptData.id = this.currentEditingPrompt.id;
        }

        try {
            await promptManager.savePrompt(promptData);
            this.closePromptEditor();
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            await this.loadTagFilters();
            this.showSuccess(this.currentEditingPrompt ? 'Prompt updated successfully' : 'Prompt created successfully');
        } catch (error) {
            console.error('Error saving prompt:', error);
            this.showError('Failed to save prompt');
        }
    }

    async deletePromptConfirm(promptId) {
        if (confirm('Are you sure you want to delete this prompt? This action cannot be undone.')) {
            await this.deletePrompt(promptId);
        }
    }

    async deletePrompt(promptId) {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.showError('Unable to verify Pro status. Please try again.');
            return;
        }

        try {
            await promptManager.deletePrompt(promptId);
            
            // Check if current page is now empty and adjust if needed
            let allPrompts = await promptManager.getPrompts();
            const sortBy = document.getElementById('promptSort').value;
            allPrompts = promptManager.sortPrompts(allPrompts, sortBy);
            
            // Apply current filter
            if (this.currentFilterTag) {
                allPrompts = allPrompts.filter(p => p.tags.includes(this.currentFilterTag));
            }
            
            const currentPageData = this.promptPagination.getPaginatedData(allPrompts);
            if (currentPageData.data.length === 0 && this.promptPagination.currentPage > 1) {
                this.promptPagination.currentPage = Math.max(1, this.promptPagination.currentPage - 1);
            }
            
            await this.refreshPromptList();
            await this.loadSavedPromptsSelect();
            await this.loadTagFilters();
            this.showSuccess('Prompt deleted successfully');
        } catch (error) {
            console.error('Error deleting prompt:', error);
            this.showError('Failed to delete prompt');
        }
    }

    async copyPromptContent(promptId) {
        try {
            const result = await promptManager.copyPromptContent(promptId);
            if (result.success) {
                this.showSuccess('Prompt content copied to clipboard!');
            } else {
                this.showError('Failed to copy prompt content: ' + (result.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error copying prompt content:', error);
            this.showError('Failed to copy prompt content');
        }
    }

    async exportPrompts() {
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.showError('Unable to verify Pro status. Please try again.');
            return;
        }

        try {
            const jsonData = await promptManager.exportPrompts();
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `hustleplug-prompts-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            this.showSuccess('Prompts exported successfully');
        } catch (error) {
            console.error('Error exporting prompts:', error);
            this.showError('Failed to export prompts');
        }
    }

    async importPrompts(file) {
        if (!file) return;
        
        try {
            const proStatus = await checkProStatus();
            if (!proStatus.isPro) {
                this.showError('Prompt management is a Pro feature. Please upgrade to continue.');
                this.showSection('upgradeSection');
                return;
            }
        } catch (error) {
            console.error('Error checking pro status:', error);
            this.showError('Unable to verify Pro status. Please try again.');
            return;
        }
        
        try {
            const text = await file.text();
            const result = await promptManager.importPrompts(text);
            
            if (result.success) {
                await this.refreshPromptList();
                await this.loadSavedPromptsSelect();
                await this.loadTagFilters();
                this.showSuccess(`Imported ${result.imported} prompts successfully`);
            } else {
                this.showError(`Import failed: ${result.error}`);
            }
        } catch (error) {
            console.error('Error importing prompts:', error);
            this.showError('Failed to import prompts');
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    async sendAnalysisToTelegram(analysisId) {
        try {
            // Get the analysis data
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (!analysisItem) {
                this.showError('Could not find the selected analysis.');
                return;
            }

            // Get Telegram settings
            const settings = await getTelegramSettings();
            if (!settings) {
                this.showError('Telegram not configured. Please configure Telegram in Settings.');
                return;
            }

            // Show sending state
            const telegramBtn = document.querySelector(`.telegram-send-btn[data-analysis-id="${analysisId}"]`);
            if (telegramBtn) {
                telegramBtn.disabled = true;
                telegramBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }

            // Prepare analysis data for Telegram
            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: analysisItem.date
            };

            // Send to Telegram
            const result = await sendAnalysisToTelegram(analysisData, settings.botToken, settings.chatId);

            if (result.success) {
                this.showSuccess(`Analysis sent to Telegram successfully! ${result.messageCount > 1 ? `(${result.messageCount} messages)` : ''}`);
            } else {
                this.showError(`Failed to send to Telegram: ${result.error}`);
            }

        } catch (error) {
            console.error('Error sending analysis to Telegram:', error);
            this.showError('Failed to send analysis to Telegram');
        } finally {
            // Restore button state
            const telegramBtn = document.querySelector(`.telegram-send-btn[data-analysis-id="${analysisId}"]`);
            if (telegramBtn) {
                telegramBtn.disabled = false;
                telegramBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M21 3L3 10.5L10.5 13.5L13.5 21L21 3Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M13.5 13.5L21 3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }
        }
    }

    async sendAnalysisToDiscord(analysisId) {
        try {
            // Get the analysis data
            const data = await chrome.storage.local.get(['hustleplugAnalysis']);
            const history = data.hustleplugAnalysis || [];
            const analysisItem = history.find(item => item.id === analysisId);

            if (!analysisItem) {
                this.showError('Could not find the selected analysis.');
                return;
            }

            // Get Discord settings
            const settings = await getDiscordSettings();
            if (!settings) {
                this.showError('Discord not configured. Please configure Discord in Settings.');
                return;
            }

            // Show sending state
            const discordBtn = document.querySelector(`.discord-send-btn[data-analysis-id="${analysisId}"]`);
            if (discordBtn) {
                discordBtn.disabled = true;
                discordBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
                        <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                `;
            }

            // Prepare analysis data for Discord
            const analysisData = {
                analysisType: analysisItem.analysisType,
                result: analysisItem.result,
                date: analysisItem.date
            };

            // Send to Discord
            const result = await sendAnalysisToDiscord(analysisData, settings.webhookUrl);

            if (result.success) {
                this.showSuccess('Analysis sent to Discord successfully!');
            } else {
                this.showError(`Failed to send to Discord: ${result.error}`);
            }

        } catch (error) {
            console.error('Error sending analysis to Discord:', error);
            this.showError('Failed to send analysis to Discord');
        } finally {
            // Restore button state
            const discordBtn = document.querySelector(`.discord-send-btn[data-analysis-id="${analysisId}"]`);
            if (discordBtn) {
                discordBtn.disabled = false;
                discordBtn.innerHTML = `
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-1.25.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 3.03.078.078 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.12.098.246.198.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                `;
            }
        }
    }
}

// Initialize the analyzer when the popup loads
document.addEventListener('DOMContentLoaded', () => {
    window.analyzer = new AgentHustleAnalyzer();
}); 