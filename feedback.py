#!/usr/bin/env python3
"""
Interactive Feedback Script
Based on interactive-feedback-mcp by <PERSON><PERSON><PERSON>
Simplified version for direct LLM integration
"""

import os
import sys
import json
import argparse
import subprocess
import tempfile
import threading
import time
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import psutil
except ImportError:
    psutil = None

try:
    import tkinter as tk
    from tkinter import scrolledtext, messagebox
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False

def get_terminal_size():
    """Get terminal size or default to 80x24"""
    try:
        import shutil
        columns, rows = shutil.get_terminal_size()
        return columns, rows
    except:
        return 80, 24

def print_header(title: str, char: str = "="):
    """Print a header with dynamic width"""
    columns, _ = get_terminal_size()
    print(char * columns)
    print(title)
    print(char * columns)

def gui_feedback_editor(initial_text: str = "") -> Optional[str]:
    """Open a GUI editor for feedback input"""
    if not GUI_AVAILABLE:
        return None
    
    result = {"feedback": None, "submitted": False}
    
    def on_submit():
        result["feedback"] = text_area.get("1.0", tk.END).strip()
        result["submitted"] = True
        root.destroy()
    
    def on_cancel():
        result["submitted"] = False
        root.destroy()
    
    # Create the main window
    root = tk.Tk()
    root.title("Feedback Editor")
    root.geometry("600x400")
    
    # Create text area
    text_area = scrolledtext.ScrolledText(root, wrap=tk.WORD, width=70, height=20)
    text_area.pack(expand=True, fill='both', padx=10, pady=10)
    
    # Insert initial text if provided
    if initial_text:
        text_area.insert("1.0", initial_text)
    
    # Create button frame
    button_frame = tk.Frame(root)
    button_frame.pack(pady=5)
    
    # Create buttons
    submit_btn = tk.Button(button_frame, text="Submit Feedback", command=on_submit, bg="green", fg="white")
    submit_btn.pack(side=tk.LEFT, padx=5)
    
    cancel_btn = tk.Button(button_frame, text="Cancel", command=on_cancel, bg="red", fg="white")
    cancel_btn.pack(side=tk.LEFT, padx=5)
    
    # Bind Ctrl+Enter to submit
    root.bind('<Control-Return>', lambda e: on_submit())
    
    # Focus on text area
    text_area.focus_set()
    
    # Start the GUI
    root.mainloop()
    
    return result["feedback"] if result["submitted"] else None

def terminal_feedback_input() -> Optional[str]:
    """Collect feedback via terminal with improved input handling"""
    print("Type your feedback and press Enter to submit.")
    print("For multi-line feedback, type your message and then type 'DONE' on a new line.")
    print("Type 'EDIT' to open GUI editor (if available).")
    print("-" * get_terminal_size()[0])
    
    lines = []
    try:
        while True:
            line = input()
            
            # Check for special commands
            if line.strip().upper() == "DONE":
                break
            elif line.strip().upper() == "EDIT" and GUI_AVAILABLE:
                current_text = "\n".join(lines)
                gui_result = gui_feedback_editor(current_text)
                if gui_result is not None:
                    return gui_result
                # If GUI was cancelled, continue with terminal input
                print("GUI cancelled. Continuing with terminal input...")
                continue
            
            lines.append(line)
            
            # If it's the first line and doesn't contain special commands, 
            # ask if they want to add more lines
            if len(lines) == 1 and line.strip().upper() not in ["DONE", "EDIT"]:
                print("Press Enter again to submit, or type more lines (end with 'DONE'):")
                next_line = input()
                if next_line.strip() == "":
                    # Empty line means submit the first line
                    break
                else:
                    # Add the second line and continue collecting
                    lines.append(next_line)
                    continue
                    
    except KeyboardInterrupt:
        print("\nFeedback collection cancelled.")
        return None
    
    return "\n".join(lines)

def interactive_feedback(project_directory: str = None, summary: str = None, use_gui: bool = False) -> Dict[str, Any]:
    """Collect interactive feedback from the user"""
    project_directory = project_directory or os.getcwd()
    
    # Clear screen for better visibility
    os.system('cls' if os.name == 'nt' else 'clear')
    print("\n" * 3)  # Add some spacing
    
    print_header("INTERACTIVE FEEDBACK REQUEST")
    print(f"Working directory: {project_directory}\n")

    if summary:
        print("Summary:", summary)
        print()

    # Try GUI first if requested and available
    feedback = None
    if use_gui and GUI_AVAILABLE:
        print("Opening GUI editor...")
        feedback = gui_feedback_editor()
        if feedback is None:
            print("GUI cancelled. Falling back to terminal input.\n")
    
    # Fall back to terminal input
    if feedback is None:
        print_header("Please provide your feedback:", char="-")
        feedback = terminal_feedback_input()
    
    if feedback is None:
        return {"interactive_feedback": None, "cancelled": True}

    print(f"\nFeedback collected: {len(feedback)} characters\n")

    print_header("FEEDBACK SUMMARY")
    print("User feedback:")
    print(feedback)
    print("=" * get_terminal_size()[0])
    print()

    return {
        "interactive_feedback": feedback,
        "cancelled": False,
        "project_directory": project_directory,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }

def main():
    parser = argparse.ArgumentParser(description="Interactive Feedback Collection")
    parser.add_argument("--summary", help="Optional summary or context for the feedback request", default=None)
    parser.add_argument("--dir", help="Project directory (defaults to current directory)", default=None)
    parser.add_argument("--gui", action="store_true", help="Try to use GUI editor first")
    
    args = parser.parse_args()
    result = interactive_feedback(args.dir, args.summary, args.gui)
    
    if not result["cancelled"]:
        # Save feedback to file
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        feedback_file = Path(result["project_directory"]) / f"feedback_{timestamp}.json"
        with open(feedback_file, "w") as f:
            json.dump(result, f, indent=2)
        print(f"Feedback saved to: {feedback_file}")

if __name__ == "__main__":
    main() 