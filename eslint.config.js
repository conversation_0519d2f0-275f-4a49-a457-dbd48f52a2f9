const sonarjs = require("eslint-plugin-sonarjs");

module.exports = [
  {
    files: ["js/**/*.js"],
    languageOptions: {
      ecmaVersion: 2021,
      sourceType: "module",
      globals: {
        browser: "readonly",
        chrome: "readonly",
        console: "readonly",
        document: "readonly",
        window: "readonly",
        setTimeout: "readonly",
        setInterval: "readonly",
        Promise: "readonly",
        fetch: "readonly",
        localStorage: "readonly",
        importScripts: "readonly",
        self: "readonly",
        crypto: "readonly",
        SubtleCrypto: "readonly"
      }
    },
    plugins: {
      sonarjs,
    },
    rules: {
      ...sonarjs.configs.recommended.rules,

      // Enforce a maximum cyclomatic complexity
      "complexity": ["warn", 15],

      // SonarJS rules for detecting "code smells"
      "sonarjs/cognitive-complexity": ["warn", 20],
      "sonarjs/no-duplicate-string": ["warn", { "threshold": 5 }],
      "sonarjs/no-identical-functions": "warn",
      
      // General Best Practices
      "no-unused-vars": "warn",
      "no-debugger": "error",
      "no-console": "off",
    },
  },
  {
    ignores: ["node_modules/", "dist/", "vercel-api/"],
  }
]; 