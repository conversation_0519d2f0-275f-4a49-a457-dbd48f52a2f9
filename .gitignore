# Chrome Extension specific
*.crx
*.pem
key.pem
*.zip

# Node modules and package files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
config.json
secrets.json

# API Keys and sensitive data
api-keys.txt
*.key
*.secret

# Build and distribution
dist/
build/
.tmp/
temp/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Cache directories
.cache/
.parcel-cache/

# Testing
coverage/
.nyc_output/
test-*.js
test_*.js
*-test.js
*_test.js
debug-*.js
debug_*.js

# Temporary files
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Development tools
.eslintcache
.stylelintcache

# Chrome extension development
manifest.json.backup
popup.html.backup
*.backup 

# YoYo AI version control directory
.yoyo/
.context/
context/
backups/

# Analysis and inventory output files
*-complexity.json
*-inventory.json
--output
proValidator-complexity.json
proValidator-inventory.json

# Python development files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Python package files
setup.py
requirements.txt

# Refactoring and analysis documentation
*REFACTORING*.md
COMPREHENSIVE_REFACTORING_PROMPT.md
CSS_REFACTORING_PROMPT.md
FUNCTION_REFACTORING_PROMPT.md
UNIVERSAL_FILE_REFACTORING_PROMPT.md
UNIVERSAL_REFACTORING_PROMPT.md

# Cursor AI rules
.cursor/

# Performance test files
test-idle-validation-performance.js

# -------------------------------
# HustlePlug additional ignore rules (change logs & scripts)
# -------------------------------

# Development change-log directories & files
changes/
CODEBASE_CHANGES_*.md
SESSION_CHANGES_*.md
API_UPDATE_SUMMARY.md
VERIFICATION_DELAY_FIX_SUMMARY.md

# Backup CSS and other generated backups
styles/*_backup_*.css
styles/popup_backup_*.css

# PowerShell helper scripts
*.ps1

# Inventory / analysis dumps
original-inventory.txt
method_inventory.py
method-comparison-fix.js