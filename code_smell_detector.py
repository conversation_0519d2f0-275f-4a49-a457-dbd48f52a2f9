#!/usr/bin/env python3
"""
Code Smell Detector for JavaScript
Detects anti-patterns and design problems that indicate refactoring needs
"""

import re
import json
import sys
from pathlib import Path
from typing import List, Dict, Any, Tuple
from collections import defaultdict

class CodeSmellDetector:
    def __init__(self):
        self.smells = []
        self.severity_weights = {'CRITICAL': 10, 'HIGH': 5, 'MEDIUM': 3, 'LOW': 1}
    
    def detect_long_functions(self, content: str, file_path: str) -> None:
        """Detect functions longer than recommended limits"""
        lines = content.split('\n')
        function_pattern = r'(?:function\s+(\w+)|(\w+)\s*[:=]\s*(?:async\s+)?function|(\w+)\s*=\s*(?:async\s*)?\([^)]*\)\s*=>)'
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            match = re.search(function_pattern, line)
            
            if match:
                function_name = match.group(1) or match.group(2) or match.group(3) or f"anonymous_{i+1}"
                
                # Find function body
                brace_count = 0
                function_start = i
                function_lines = []
                
                j = i
                while j < len(lines):
                    current_line = lines[j]
                    function_lines.append(current_line)
                    brace_count += current_line.count('{') - current_line.count('}')
                    
                    if brace_count == 0 and '{' in ''.join(function_lines):
                        break
                    j += 1
                
                function_length = len(function_lines)
                
                if function_length > 100:
                    severity = 'CRITICAL'
                elif function_length > 50:
                    severity = 'HIGH'
                elif function_length > 30:
                    severity = 'MEDIUM'
                else:
                    severity = None
                
                if severity:
                    self.smells.append({
                        'type': 'LONG_FUNCTION',
                        'severity': severity,
                        'file': file_path,
                        'line': function_start + 1,
                        'function': function_name,
                        'metric': function_length,
                        'threshold': 30,
                        'message': f'Function "{function_name}" is {function_length} lines long',
                        'suggestion': 'Break into smaller, focused functions',
                        'impact': 'Reduces readability and maintainability'
                    })
                
                i = j + 1
            else:
                i += 1
    
    def detect_deep_nesting(self, content: str, file_path: str) -> None:
        """Detect deeply nested code structures"""
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            # Calculate indentation level (assuming 2 or 4 spaces)
            stripped = line.lstrip()
            if not stripped or stripped.startswith('//'):
                continue
            
            indent_chars = len(line) - len(stripped)
            indent_level = indent_chars // 2  # Assume 2-space indentation
            
            if indent_level > 6:
                severity = 'HIGH'
            elif indent_level > 4:
                severity = 'MEDIUM'
            else:
                continue
            
            self.smells.append({
                'type': 'DEEP_NESTING',
                'severity': severity,
                'file': file_path,
                'line': i + 1,
                'metric': indent_level,
                'threshold': 4,
                'message': f'Deep nesting level {indent_level} detected',
                'suggestion': 'Extract nested logic into separate functions or use early returns',
                'impact': 'Increases cognitive complexity and error likelihood'
            })
    
    def detect_duplicate_code(self, content: str, file_path: str) -> None:
        """Detect potential code duplication"""
        lines = content.split('\n')
        line_groups = defaultdict(list)
        
        # Group similar lines (ignoring whitespace and comments)
        for i, line in enumerate(lines):
            clean_line = re.sub(r'\s+', ' ', line.strip())
            if len(clean_line) > 15 and not clean_line.startswith('//'):
                line_groups[clean_line].append(i + 1)
        
        # Find duplicates
        for line_content, line_numbers in line_groups.items():
            if len(line_numbers) >= 3:  # 3 or more occurrences
                severity = 'HIGH' if len(line_numbers) >= 5 else 'MEDIUM'
                
                self.smells.append({
                    'type': 'DUPLICATE_CODE',
                    'severity': severity,
                    'file': file_path,
                    'lines': line_numbers,
                    'metric': len(line_numbers),
                    'threshold': 2,
                    'message': f'Code duplicated {len(line_numbers)} times: "{line_content[:50]}..."',
                    'suggestion': 'Extract common code into a reusable function',
                    'impact': 'Increases maintenance burden and bug risk'
                })
    
    def detect_too_many_parameters(self, content: str, file_path: str) -> None:
        """Detect functions with too many parameters"""
        # Pattern to match function declarations with parameters
        patterns = [
            r'function\s+(\w+)\s*\(([^)]+)\)',
            r'(\w+)\s*[:=]\s*function\s*\(([^)]+)\)',
            r'(\w+)\s*=\s*\(([^)]+)\)\s*=>',
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content)
            
            for match in matches:
                function_name = match.group(1)
                params_str = match.group(2)
                
                # Count parameters (split by comma, but be careful with nested structures)
                params = [p.strip() for p in params_str.split(',') if p.strip()]
                param_count = len(params)
                
                if param_count > 7:
                    severity = 'HIGH'
                elif param_count > 5:
                    severity = 'MEDIUM'
                else:
                    continue
                
                line_num = content[:match.start()].count('\n') + 1
                
                self.smells.append({
                    'type': 'TOO_MANY_PARAMETERS',
                    'severity': severity,
                    'file': file_path,
                    'line': line_num,
                    'function': function_name,
                    'metric': param_count,
                    'threshold': 5,
                    'message': f'Function "{function_name}" has {param_count} parameters',
                    'suggestion': 'Use parameter objects or split into smaller functions',
                    'impact': 'Makes function calls complex and error-prone'
                })
    
    def detect_large_classes(self, content: str, file_path: str) -> None:
        """Detect classes that are too large"""
        class_pattern = r'class\s+(\w+)(?:\s+extends\s+\w+)?\s*{'
        matches = re.finditer(class_pattern, content)
        
        for match in matches:
            class_name = match.group(1)
            class_start = match.start()
            
            # Find class end by counting braces
            brace_count = 0
            class_content = content[class_start:]
            class_end = class_start
            
            for i, char in enumerate(class_content):
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        class_end = class_start + i
                        break
            
            class_body = content[class_start:class_end]
            class_lines = class_body.count('\n')
            method_count = len(re.findall(r'^\s*(\w+)\s*\([^)]*\)\s*{', class_body, re.MULTILINE))
            
            if class_lines > 300:
                severity = 'HIGH'
            elif class_lines > 200:
                severity = 'MEDIUM'
            elif method_count > 20:
                severity = 'MEDIUM'
            else:
                continue
            
            line_num = content[:class_start].count('\n') + 1
            
            self.smells.append({
                'type': 'LARGE_CLASS',
                'severity': severity,
                'file': file_path,
                'line': line_num,
                'class': class_name,
                'metric': class_lines,
                'threshold': 200,
                'message': f'Class "{class_name}" is {class_lines} lines with {method_count} methods',
                'suggestion': 'Split into smaller, focused classes',
                'impact': 'Violates single responsibility principle'
            })
    
    def detect_complex_conditionals(self, content: str, file_path: str) -> None:
        """Detect overly complex conditional statements"""
        lines = content.split('\n')
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if not stripped.startswith('if') and not stripped.startswith('while'):
                continue
            
            # Count logical operators
            and_count = len(re.findall(r'&&', stripped))
            or_count = len(re.findall(r'\|\|', stripped))
            total_operators = and_count + or_count
            
            if total_operators > 4:
                severity = 'HIGH'
            elif total_operators > 2:
                severity = 'MEDIUM'
            else:
                continue
            
            self.smells.append({
                'type': 'COMPLEX_CONDITIONAL',
                'severity': severity,
                'file': file_path,
                'line': i + 1,
                'metric': total_operators,
                'threshold': 2,
                'message': f'Complex conditional with {total_operators} logical operators',
                'suggestion': 'Extract conditions into well-named boolean variables or functions',
                'impact': 'Reduces readability and increases bug risk'
            })
    
    def detect_magic_numbers(self, content: str, file_path: str) -> None:
        """Detect magic numbers that should be constants"""
        lines = content.split('\n')
        
        # Pattern for numeric literals (excluding 0, 1, -1 which are often acceptable)
        number_pattern = r'\b(?<![\w.])((?:[2-9]|[1-9]\d+)(?:\.\d+)?)\b(?![\w.])'
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('//') or 'const' in stripped or 'let' in stripped:
                continue
            
            matches = re.finditer(number_pattern, stripped)
            for match in matches:
                number = match.group(1)
                
                # Skip common acceptable numbers
                if number in ['2', '10', '100', '1000']:
                    continue
                
                self.smells.append({
                    'type': 'MAGIC_NUMBER',
                    'severity': 'LOW',
                    'file': file_path,
                    'line': i + 1,
                    'metric': number,
                    'message': f'Magic number "{number}" found',
                    'suggestion': 'Replace with named constant',
                    'impact': 'Reduces code maintainability and clarity'
                })
    
    def detect_long_parameter_lists(self, content: str, file_path: str) -> None:
        """Detect function calls with too many arguments"""
        # Pattern for function calls with many parameters
        call_pattern = r'(\w+)\s*\([^)]{100,}\)'  # Function calls with long parameter lists
        
        matches = re.finditer(call_pattern, content)
        
        for match in matches:
            function_name = match.group(1)
            params_str = match.group(0)
            
            # Count commas to estimate parameter count
            param_count = params_str.count(',') + 1
            
            if param_count > 5:
                line_num = content[:match.start()].count('\n') + 1
                
                self.smells.append({
                    'type': 'LONG_PARAMETER_LIST',
                    'severity': 'MEDIUM',
                    'file': file_path,
                    'line': line_num,
                    'function': function_name,
                    'metric': param_count,
                    'threshold': 5,
                    'message': f'Function call "{function_name}" has ~{param_count} arguments',
                    'suggestion': 'Use parameter objects or builder pattern',
                    'impact': 'Makes function calls hard to understand and maintain'
                })
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Run all smell detection on a file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f"Could not read file: {e}"}
        
        # Clear previous smells for this file
        self.smells = [s for s in self.smells if s.get('file') != file_path]
        
        # Run all detectors
        self.detect_long_functions(content, file_path)
        self.detect_deep_nesting(content, file_path)
        self.detect_duplicate_code(content, file_path)
        self.detect_too_many_parameters(content, file_path)
        self.detect_large_classes(content, file_path)
        self.detect_complex_conditionals(content, file_path)
        self.detect_magic_numbers(content, file_path)
        self.detect_long_parameter_lists(content, file_path)
        
        # Filter smells for this file
        file_smells = [s for s in self.smells if s.get('file') == file_path]
        
        # Calculate statistics
        severity_counts = defaultdict(int)
        type_counts = defaultdict(int)
        
        for smell in file_smells:
            severity_counts[smell['severity']] += 1
            type_counts[smell['type']] += 1
        
        # Calculate smell score
        smell_score = sum(self.severity_weights[s['severity']] for s in file_smells)
        
        return {
            'file': file_path,
            'total_smells': len(file_smells),
            'smell_score': smell_score,
            'severity_counts': dict(severity_counts),
            'type_counts': dict(type_counts),
            'smells': file_smells,
            'refactoring_priority': self.get_refactoring_priority(smell_score)
        }
    
    def get_refactoring_priority(self, smell_score: int) -> str:
        """Determine refactoring priority based on smell score"""
        if smell_score >= 50:
            return 'CRITICAL'
        elif smell_score >= 25:
            return 'HIGH'
        elif smell_score >= 10:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def generate_report(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """Generate detailed code smell analysis report"""
        if 'error' in analysis:
            return f"Error: {analysis['error']}"
        
        report = []
        report.append("🔍 Code Smell Analysis Report")
        report.append("=" * 50)
        report.append("")
        
        # Summary
        report.append(f"📊 Summary for {analysis['file']}:")
        report.append(f"  • Total code smells: {analysis['total_smells']}")
        report.append(f"  • Smell score: {analysis['smell_score']}")
        report.append(f"  • Refactoring priority: {analysis['refactoring_priority']}")
        report.append("")
        
        # Severity breakdown
        if analysis['severity_counts']:
            report.append("📈 Severity Distribution:")
            severity_emojis = {'CRITICAL': '🔴', 'HIGH': '🟠', 'MEDIUM': '🟡', 'LOW': '🟢'}
            for severity, count in analysis['severity_counts'].items():
                emoji = severity_emojis.get(severity, '⚪')
                report.append(f"  {emoji} {severity}: {count}")
            report.append("")
        
        # Type breakdown
        if analysis['type_counts']:
            report.append("🏷️  Smell Types:")
            for smell_type, count in analysis['type_counts'].items():
                readable_type = smell_type.replace('_', ' ').title()
                report.append(f"  • {readable_type}: {count}")
            report.append("")
        
        # Detailed smells
        if analysis['smells']:
            # Group by severity
            smells_by_severity = defaultdict(list)
            for smell in analysis['smells']:
                smells_by_severity[smell['severity']].append(smell)
            
            for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
                if severity in smells_by_severity:
                    emoji = {'CRITICAL': '🔴', 'HIGH': '🟠', 'MEDIUM': '🟡', 'LOW': '🟢'}[severity]
                    report.append(f"{emoji} {severity} Priority Issues:")
                    report.append("-" * 30)
                    
                    for smell in smells_by_severity[severity]:
                        report.append(f"📍 Line {smell['line']}: {smell['message']}")
                        report.append(f"   Type: {smell['type'].replace('_', ' ').title()}")
                        report.append(f"   Suggestion: {smell['suggestion']}")
                        report.append(f"   Impact: {smell['impact']}")
                        report.append("")
        
        # Refactoring recommendations
        report.append("🎯 Refactoring Recommendations:")
        report.append("-" * 30)
        
        priority = analysis['refactoring_priority']
        if priority == 'CRITICAL':
            report.append("🚨 URGENT: This file needs immediate refactoring")
            report.append("   - Focus on breaking down large functions and classes")
            report.append("   - Reduce complexity before making other changes")
        elif priority == 'HIGH':
            report.append("⚠️  HIGH: Significant refactoring needed")
            report.append("   - Address high-severity issues first")
            report.append("   - Consider splitting into multiple files")
        elif priority == 'MEDIUM':
            report.append("📋 MEDIUM: Some refactoring would be beneficial")
            report.append("   - Address issues incrementally")
            report.append("   - Good candidate for gradual improvement")
        else:
            report.append("✅ LOW: Code quality is acceptable")
            report.append("   - Minor improvements possible")
            report.append("   - Focus on other files first")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 Report saved to: {output_file}")
        
        return report_text

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 code_smell_detector.py <javascript-file> [output-file]")
        print("Example: python3 code_smell_detector.py popup.js smells-report.txt")
        sys.exit(1)
    
    file_path = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(file_path).exists():
        print(f"❌ Error: File '{file_path}' not found")
        sys.exit(1)
    
    print(f"🔍 Analyzing code smells in {file_path}...")
    
    detector = CodeSmellDetector()
    analysis = detector.analyze_file(file_path)
    
    if 'error' in analysis:
        print(f"❌ {analysis['error']}")
        sys.exit(1)
    
    # Generate and display report
    report = detector.generate_report(analysis, output_file)
    print(report)
    
    # Save JSON data
    json_file = f"{Path(file_path).stem}-smells.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2)
    print(f"📊 JSON data saved to: {json_file}")
    
    # Summary recommendation
    priority = analysis['refactoring_priority']
    smell_count = analysis['total_smells']
    
    if priority in ['CRITICAL', 'HIGH']:
        print(f"\n🎯 RECOMMENDATION: {priority} priority refactoring needed")
        print(f"   Found {smell_count} code smells that should be addressed")
    else:
        print(f"\n✅ Code quality is acceptable ({priority} priority)")
        print(f"   Found {smell_count} minor issues that could be improved")

if __name__ == "__main__":
    main() 