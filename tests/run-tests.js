#!/usr/bin/env node

// Test Runner for Permanent Pro Status System
// Runs all tests and generates performance reports

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Running Permanent Pro Status System Tests\n');

// Test configuration
const testConfig = {
    testDir: __dirname,
    coverageDir: path.join(__dirname, 'coverage'),
    reportDir: path.join(__dirname, 'reports')
};

// Ensure directories exist
if (!fs.existsSync(testConfig.coverageDir)) {
    fs.mkdirSync(testConfig.coverageDir, { recursive: true });
}
if (!fs.existsSync(testConfig.reportDir)) {
    fs.mkdirSync(testConfig.reportDir, { recursive: true });
}

// Test suites to run
const testSuites = [
    {
        name: 'Unit Tests',
        pattern: 'permanentProStatus.test.js',
        description: 'Core permanent storage functionality'
    },
    {
        name: 'Integration Tests',
        pattern: 'integration.test.js',
        description: 'Complete validation flow integration'
    },
    {
        name: 'Performance Tests',
        pattern: 'performance.test.js',
        description: 'Performance validation and benchmarks'
    }
];

// Performance thresholds
const performanceThresholds = {
    permanentStatusCheck: 100, // ms
    completeValidation: 100,   // ms
    storageOperation: 200,     // ms
    migration: 500            // ms
};

async function runTests() {
    let allTestsPassed = true;
    const results = {
        suites: [],
        performance: {},
        coverage: null,
        summary: {
            total: 0,
            passed: 0,
            failed: 0,
            duration: 0
        }
    };

    const overallStartTime = Date.now();

    for (const suite of testSuites) {
        console.log(`\n📋 Running ${suite.name}: ${suite.description}`);
        console.log('─'.repeat(60));

        try {
            const startTime = Date.now();
            
            // Run the test suite
            const command = `npx jest ${suite.pattern} --config=jest.config.js --verbose --json`;
            const output = execSync(command, { 
                cwd: testConfig.testDir,
                encoding: 'utf8',
                stdio: ['pipe', 'pipe', 'pipe']
            });

            const endTime = Date.now();
            const duration = endTime - startTime;

            // Parse Jest output
            const testResult = JSON.parse(output);
            
            const suiteResult = {
                name: suite.name,
                passed: testResult.success,
                duration: duration,
                tests: testResult.numTotalTests,
                failures: testResult.numFailedTests,
                details: testResult.testResults
            };

            results.suites.push(suiteResult);
            results.summary.total += testResult.numTotalTests;
            results.summary.passed += testResult.numPassedTests;
            results.summary.failed += testResult.numFailedTests;

            if (testResult.success) {
                console.log(`✅ ${suite.name} passed (${duration}ms)`);
            } else {
                console.log(`❌ ${suite.name} failed (${duration}ms)`);
                allTestsPassed = false;
            }

        } catch (error) {
            console.log(`❌ ${suite.name} failed with error:`);
            console.log(error.message);
            allTestsPassed = false;
            
            results.suites.push({
                name: suite.name,
                passed: false,
                error: error.message,
                duration: 0,
                tests: 0,
                failures: 1
            });
            results.summary.failed += 1;
        }
    }

    results.summary.duration = Date.now() - overallStartTime;

    // Generate coverage report
    console.log('\n📊 Generating Coverage Report...');
    try {
        execSync('npx jest --coverage --coverageReporters=json-summary', {
            cwd: testConfig.testDir,
            stdio: 'pipe'
        });

        const coveragePath = path.join(testConfig.coverageDir, 'coverage-summary.json');
        if (fs.existsSync(coveragePath)) {
            results.coverage = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        }
    } catch (error) {
        console.log('⚠️ Coverage report generation failed:', error.message);
    }

    // Generate performance report
    console.log('\n⚡ Performance Analysis...');
    generatePerformanceReport(results);

    // Generate final report
    generateFinalReport(results, allTestsPassed);

    return allTestsPassed;
}

function generatePerformanceReport(results) {
    const performanceResults = {
        thresholds: performanceThresholds,
        actual: {},
        status: 'PASS'
    };

    // Extract performance metrics from test results
    // This would be populated by actual test execution
    console.log('Performance Thresholds:');
    Object.entries(performanceThresholds).forEach(([metric, threshold]) => {
        console.log(`  ${metric}: < ${threshold}ms`);
    });

    // Save performance report
    const reportPath = path.join(testConfig.reportDir, 'performance-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(performanceResults, null, 2));
}

function generateFinalReport(results, allTestsPassed) {
    console.log('\n' + '='.repeat(60));
    console.log('📋 TEST SUMMARY');
    console.log('='.repeat(60));

    console.log(`Total Tests: ${results.summary.total}`);
    console.log(`Passed: ${results.summary.passed}`);
    console.log(`Failed: ${results.summary.failed}`);
    console.log(`Duration: ${results.summary.duration}ms`);

    if (results.coverage) {
        console.log('\n📊 COVERAGE SUMMARY');
        console.log('─'.repeat(30));
        const total = results.coverage.total;
        console.log(`Lines: ${total.lines.pct}%`);
        console.log(`Functions: ${total.functions.pct}%`);
        console.log(`Branches: ${total.branches.pct}%`);
        console.log(`Statements: ${total.statements.pct}%`);
    }

    console.log('\n📋 SUITE RESULTS');
    console.log('─'.repeat(30));
    results.suites.forEach(suite => {
        const status = suite.passed ? '✅' : '❌';
        console.log(`${status} ${suite.name}: ${suite.tests} tests, ${suite.duration}ms`);
    });

    // Save full report
    const reportPath = path.join(testConfig.reportDir, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));

    console.log(`\n📄 Full report saved to: ${reportPath}`);

    if (allTestsPassed) {
        console.log('\n🎉 All tests passed! Permanent Pro Status system is ready for deployment.');
    } else {
        console.log('\n❌ Some tests failed. Please review and fix issues before deployment.');
    }

    return allTestsPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
    runTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Test runner failed:', error);
            process.exit(1);
        });
}

module.exports = { runTests, testConfig, performanceThresholds };
