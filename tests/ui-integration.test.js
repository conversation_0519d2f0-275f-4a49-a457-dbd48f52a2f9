// UI Integration Tests for Permanent Pro Status System
// Tests complete flow with existing UI to ensure no functionality is broken

import { validateProKey } from '../js/auth/proValidator.js';
import { checkProStatus } from '../js/user/proStatus.js';
import { 
    checkPermanentProStatus, 
    storePermanentProStatus,
    migrateCacheToPermanent 
} from '../js/auth/permanentProStatus.js';

// Mock DOM elements that the UI might interact with
const mockDOM = {
    elements: {},
    createElement: jest.fn((tag) => ({
        tagName: tag.toUpperCase(),
        innerHTML: '',
        textContent: '',
        style: {},
        classList: {
            add: jest.fn(),
            remove: jest.fn(),
            contains: jest.fn()
        },
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        appendChild: jest.fn(),
        removeChild: jest.fn()
    })),
    getElementById: jest.fn((id) => mockDOM.elements[id] || null),
    querySelector: jest.fn((selector) => mockDOM.elements[selector] || null)
};

// Setup global DOM mock
global.document = mockDOM;
global.window = {
    location: { href: 'chrome-extension://test/popup.html' },
    chrome: global.chrome
};

// Mock the hash function
jest.mock('../js/security/hashUtils.js', () => ({
    hashKey: jest.fn((key) => Promise.resolve('abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'))
}));

describe('UI Integration Tests', () => {
    beforeEach(() => {
        // Reset Chrome storage
        chrome.storage.sync.get.mockClear();
        chrome.storage.sync.set.mockClear();
        chrome.storage.local.get.mockClear();
        
        // Reset DOM elements
        mockDOM.elements = {};
        jest.clearAllMocks();
    });

    describe('Pro Status Display Integration', () => {
        test('should display permanent Pro status correctly', async () => {
            // Setup permanent Pro status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': testUtils.createMockProStatus()
                    });
                }
                return Promise.resolve({});
            });

            const result = await checkProStatus();

            // Should return Pro status with permanent flag
            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(result.cached).toBe(false);
            expect(result.membershipDetails).toBeDefined();

            // Should store status with permanent information
            expect(chrome.storage.sync.set).toHaveBeenCalledWith({
                hustleProStatus: expect.objectContaining({
                    isPro: true,
                    permanent: true,
                    migrated: false,
                    membershipDetails: expect.any(Object)
                })
            });
        });

        test('should handle UI updates for migrated users', async () => {
            // Setup Pro key and cached status for migration
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123',
                        hustleProStatus: {
                            isPro: true,
                            lastChecked: '2025-01-15T10:30:00.000Z'
                        }
                    });
                }
                return Promise.resolve({});
            });

            chrome.storage.local.get.mockImplementation(() => {
                return Promise.resolve({
                    'proStatus_abcdef1234567890': {
                        isPro: true,
                        lastValidated: '2025-01-15T10:30:00.000Z',
                        membershipDetails: {
                            tier: 'pro',
                            status: 'active',
                            expiresAt: '2026-01-15T10:30:00.000Z'
                        }
                    }
                });
            });

            // Trigger migration through validation
            const result = await validateProKey('test-pro-key-123');

            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(result.migrated).toBe(true);
            expect(result.message).toBe('Pro status verified (permanent - migrated)');
        });
    });

    describe('Error Handling Integration', () => {
        test('should gracefully handle permanent storage failures in UI', async () => {
            // Mock storage failure
            chrome.storage.sync.set.mockRejectedValueOnce(new Error('Storage quota exceeded'));
            
            // Setup Pro key
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                return Promise.resolve({});
            });

            // Mock successful API validation
            const mockValidation = {
                isPro: true,
                cached: false,
                message: 'Pro user verified',
                membershipDetails: { tier: 'pro', status: 'active' }
            };

            // This would normally come from the API validation
            const result = await checkProStatus();

            // Should still work even if permanent storage fails
            expect(result).toBeDefined();
            // The exact behavior depends on how the UI handles storage failures
        });

        test('should handle expired permanent status in UI', async () => {
            // Setup expired permanent status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': testUtils.createExpiredProStatus()
                    });
                }
                return Promise.resolve({});
            });

            const result = await validateProKey('test-pro-key-123');

            // Should fall back to API validation when permanent status is expired
            // The exact behavior depends on the API validation mock
            expect(result).toBeDefined();
        });
    });

    describe('Background Process Integration', () => {
        test('should handle background migration on extension startup', async () => {
            // Setup existing Pro user data
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123',
                        hustleProStatus: {
                            isPro: true,
                            lastChecked: '2025-01-15T10:30:00.000Z'
                        }
                    });
                }
                return Promise.resolve({});
            });

            chrome.storage.local.get.mockImplementation(() => {
                return Promise.resolve({
                    'proStatus_abcdef1234567890': {
                        isPro: true,
                        lastValidated: '2025-01-15T10:30:00.000Z',
                        membershipDetails: {
                            tier: 'pro',
                            status: 'active'
                        }
                    }
                });
            });

            const result = await migrateCacheToPermanent();

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(true);
            expect(result.migratedFrom).toBe('cache');

            // Should store permanent status
            expect(chrome.storage.sync.set).toHaveBeenCalledWith(
                expect.objectContaining({
                    'permanentProStatus_abcdef1234567890': expect.objectContaining({
                        isPro: true,
                        verified: true,
                        permanent: true,
                        migrated: true
                    })
                })
            );
        });
    });

    describe('Cross-Device Synchronization Integration', () => {
        test('should sync permanent status across devices', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = {
                tier: 'pro',
                status: 'active',
                expiresAt: '2026-01-15T10:30:00.000Z'
            };

            // Store permanent status (simulating Device A)
            await storePermanentProStatus(keyHash, membershipDetails);

            // Verify it uses sync storage for cross-device compatibility
            expect(chrome.storage.sync.set).toHaveBeenCalledWith(
                expect.objectContaining({
                    'permanentProStatus_abcdef1234567890': expect.objectContaining({
                        isPro: true,
                        verified: true,
                        permanent: true,
                        keyHash: 'abcdef1234567890'
                    })
                })
            );

            // Simulate checking on Device B
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': {
                            isPro: true,
                            verified: true,
                            permanent: true,
                            verifiedAt: '2025-01-15T10:30:00.000Z',
                            keyHash: 'abcdef1234567890',
                            membershipDetails
                        }
                    });
                }
                return Promise.resolve({});
            });

            const result = await checkPermanentProStatus(keyHash);

            expect(result.verified).toBe(true);
            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
        });
    });

    describe('Performance Impact on UI', () => {
        test('should not block UI with permanent status checks', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup permanent status
            chrome.storage.sync.get.mockImplementation(() => {
                return Promise.resolve({
                    'permanentProStatus_abcdef1234567890': testUtils.createMockProStatus()
                });
            });

            // Simulate UI interaction timing
            const startTime = performance.now();
            
            // This should be fast enough not to block UI
            const result = await validateProKey('test-pro-key-123');
            
            const endTime = performance.now();
            const duration = endTime - startTime;

            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            
            // Should complete quickly to avoid UI blocking
            expect(duration).toBeLessThan(100);
        });
    });

    describe('Backward Compatibility', () => {
        test('should maintain compatibility with existing Pro validation flow', async () => {
            // Test that existing code paths still work
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                return Promise.resolve({});
            });

            const result = await checkProStatus();

            // Should return expected structure
            expect(result).toHaveProperty('isPro');
            expect(result).toHaveProperty('hasKey');
            expect(result).toHaveProperty('message');
            
            // New properties should be added without breaking existing ones
            expect(result).toHaveProperty('permanent');
            expect(result).toHaveProperty('migrated');
            expect(result).toHaveProperty('cached');
        });

        test('should handle users without permanent status gracefully', async () => {
            // Setup user with Pro key but no permanent status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                return Promise.resolve({});
            });

            chrome.storage.local.get.mockImplementation(() => {
                return Promise.resolve({});
            });

            const result = await checkProStatus();

            // Should handle gracefully and attempt validation
            expect(result).toBeDefined();
            expect(result.hasKey).toBe(true);
        });
    });

    describe('Data Integrity', () => {
        test('should maintain data consistency across operations', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = {
                tier: 'pro',
                status: 'active',
                expiresAt: '2026-01-15T10:30:00.000Z',
                createdAt: '2025-01-15T10:30:00.000Z',
                usageCount: 5
            };

            // Store permanent status
            await storePermanentProStatus(keyHash, membershipDetails);

            // Verify storage call
            const storageCall = chrome.storage.sync.set.mock.calls[0][0];
            const storedData = storageCall['permanentProStatus_abcdef1234567890'];

            expect(storedData.isPro).toBe(true);
            expect(storedData.verified).toBe(true);
            expect(storedData.permanent).toBe(true);
            expect(storedData.keyHash).toBe('abcdef1234567890');
            expect(storedData.membershipDetails).toEqual(membershipDetails);
            expect(storedData.verifiedAt).toBeDefined();
            expect(storedData.storedAt).toBeDefined();
        });
    });
});
