#!/usr/bin/env node

// Implementation Validation Script for Permanent Pro Status System
// Validates that all PRP requirements have been implemented correctly

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating Permanent Pro Status Implementation\n');

// Files that should exist
const requiredFiles = [
    'js/auth/permanentProStatus.js',
    'js/auth/proValidator.js',
    'js/user/proStatus.js',
    'background.js',
    'tests/permanentProStatus.test.js',
    'tests/integration.test.js',
    'tests/performance.test.js',
    'tests/ui-integration.test.js'
];

// Functions that should exist in permanentProStatus.js
const requiredFunctions = [
    'storePermanentProStatus',
    'checkPermanentProStatus',
    'removePermanentProStatus',
    'migrateCacheToPermanent',
    'getAllPermanentProStatuses',
    'clearAllPermanentProStatuses'
];

// Implementation requirements from PRP
const requirements = [
    {
        id: 'REQ-001',
        description: 'Permanent storage module exists',
        check: () => fs.existsSync('js/auth/permanentProStatus.js')
    },
    {
        id: 'REQ-002',
        description: 'Core validation logic modified',
        check: () => {
            const content = fs.readFileSync('js/auth/proValidator.js', 'utf8');
            return content.includes('checkPermanentProStatus') && 
                   content.includes('storePermanentProStatus');
        }
    },
    {
        id: 'REQ-003',
        description: 'Pro status check function updated',
        check: () => {
            const content = fs.readFileSync('js/user/proStatus.js', 'utf8');
            return content.includes('permanent') && content.includes('migrated');
        }
    },
    {
        id: 'REQ-004',
        description: 'Background migration implemented',
        check: () => {
            const content = fs.readFileSync('background.js', 'utf8');
            return content.includes('migrateCacheToPermanent');
        }
    },
    {
        id: 'REQ-005',
        description: 'Uses chrome.storage.sync for cross-device sync',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('chrome.storage.sync');
        }
    },
    {
        id: 'REQ-006',
        description: 'Implements proper key hashing',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('keyHash') && content.includes('permanentProStatus_');
        }
    },
    {
        id: 'REQ-007',
        description: 'Handles expiration checking',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('expiresAt') && content.includes('expired');
        }
    },
    {
        id: 'REQ-008',
        description: 'Comprehensive unit tests exist',
        check: () => fs.existsSync('tests/permanentProStatus.test.js')
    },
    {
        id: 'REQ-009',
        description: 'Performance tests exist',
        check: () => fs.existsSync('tests/performance.test.js')
    },
    {
        id: 'REQ-010',
        description: 'Integration tests exist',
        check: () => fs.existsSync('tests/integration.test.js')
    }
];

// Code quality checks
const qualityChecks = [
    {
        id: 'QC-001',
        description: 'Error handling implemented',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('try') && content.includes('catch');
        }
    },
    {
        id: 'QC-002',
        description: 'Logging implemented',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('console.log') || content.includes('console.warn');
        }
    },
    {
        id: 'QC-003',
        description: 'Proper JSDoc comments',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('/**') && content.includes('*/');
        }
    },
    {
        id: 'QC-004',
        description: 'Consistent naming conventions',
        check: () => {
            const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
            return content.includes('permanentProStatus') && !content.includes('permanent_pro_status');
        }
    }
];

// Performance requirements
const performanceChecks = [
    {
        id: 'PERF-001',
        description: 'Performance tests target <100ms',
        check: () => {
            const content = fs.readFileSync('tests/performance.test.js', 'utf8');
            return content.includes('100') && content.includes('toBeLessThan');
        }
    },
    {
        id: 'PERF-002',
        description: 'Stress testing implemented',
        check: () => {
            const content = fs.readFileSync('tests/performance.test.js', 'utf8');
            return content.includes('concurrent') || content.includes('sequential');
        }
    }
];

function validateImplementation() {
    let allPassed = true;
    const results = {
        requirements: [],
        quality: [],
        performance: [],
        files: [],
        functions: []
    };

    console.log('📁 Checking Required Files...');
    console.log('─'.repeat(50));
    
    requiredFiles.forEach(file => {
        const exists = fs.existsSync(file);
        const status = exists ? '✅' : '❌';
        console.log(`${status} ${file}`);
        
        results.files.push({ file, exists });
        if (!exists) allPassed = false;
    });

    console.log('\n🔧 Checking Required Functions...');
    console.log('─'.repeat(50));
    
    if (fs.existsSync('js/auth/permanentProStatus.js')) {
        const content = fs.readFileSync('js/auth/permanentProStatus.js', 'utf8');
        
        requiredFunctions.forEach(func => {
            const exists = content.includes(`export async function ${func}`) || 
                          content.includes(`function ${func}`) ||
                          content.includes(`const ${func} =`) ||
                          content.includes(`${func}:`);
            const status = exists ? '✅' : '❌';
            console.log(`${status} ${func}`);
            
            results.functions.push({ function: func, exists });
            if (!exists) allPassed = false;
        });
    }

    console.log('\n📋 Checking Implementation Requirements...');
    console.log('─'.repeat(50));
    
    requirements.forEach(req => {
        try {
            const passed = req.check();
            const status = passed ? '✅' : '❌';
            console.log(`${status} ${req.id}: ${req.description}`);
            
            results.requirements.push({ ...req, passed });
            if (!passed) allPassed = false;
        } catch (error) {
            console.log(`❌ ${req.id}: ${req.description} (Error: ${error.message})`);
            results.requirements.push({ ...req, passed: false, error: error.message });
            allPassed = false;
        }
    });

    console.log('\n🏆 Checking Code Quality...');
    console.log('─'.repeat(50));
    
    qualityChecks.forEach(check => {
        try {
            const passed = check.check();
            const status = passed ? '✅' : '⚠️';
            console.log(`${status} ${check.id}: ${check.description}`);
            
            results.quality.push({ ...check, passed });
        } catch (error) {
            console.log(`⚠️ ${check.id}: ${check.description} (Error: ${error.message})`);
            results.quality.push({ ...check, passed: false, error: error.message });
        }
    });

    console.log('\n⚡ Checking Performance Requirements...');
    console.log('─'.repeat(50));
    
    performanceChecks.forEach(check => {
        try {
            const passed = check.check();
            const status = passed ? '✅' : '❌';
            console.log(`${status} ${check.id}: ${check.description}`);
            
            results.performance.push({ ...check, passed });
            if (!passed) allPassed = false;
        } catch (error) {
            console.log(`❌ ${check.id}: ${check.description} (Error: ${error.message})`);
            results.performance.push({ ...check, passed: false, error: error.message });
            allPassed = false;
        }
    });

    // Generate summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 VALIDATION SUMMARY');
    console.log('='.repeat(60));

    const totalChecks = requirements.length + performanceChecks.length + requiredFiles.length + requiredFunctions.length;
    const passedChecks = results.requirements.filter(r => r.passed).length + 
                        results.performance.filter(p => p.passed).length +
                        results.files.filter(f => f.exists).length +
                        results.functions.filter(f => f.exists).length;

    console.log(`Total Checks: ${totalChecks}`);
    console.log(`Passed: ${passedChecks}`);
    console.log(`Failed: ${totalChecks - passedChecks}`);
    console.log(`Success Rate: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

    const qualityPassed = results.quality.filter(q => q.passed).length;
    console.log(`Code Quality: ${qualityPassed}/${results.quality.length} checks passed`);

    // Save detailed results
    const reportPath = 'tests/validation-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    if (allPassed) {
        console.log('\n🎉 All critical requirements passed! Implementation is ready.');
        console.log('✨ Permanent Pro Status system successfully implemented according to PRP specifications.');
    } else {
        console.log('\n❌ Some critical requirements failed. Please address issues before deployment.');
    }

    return allPassed;
}

// Run validation if this script is executed directly
if (require.main === module) {
    const success = validateImplementation();
    process.exit(success ? 0 : 1);
}

module.exports = { validateImplementation, requirements, qualityChecks, performanceChecks };
