@media (max-width: 540px) {
    .container {
        width: 100%;
        min-width: 450px;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-links {
        gap: 16px;
    }
    
    .footer-links a {
        font-size: 12px;
        padding: 6px 8px;
    }
}

.pagination-wrapper {
    margin-top: 16px;
    margin-bottom: 8px;
}

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    padding: 12px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
    font-size: 14px;
    color: #B2AFC5;
    white-space: nowrap;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
}

.pagination-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    color: #F9F9F9;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(.disabled) {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(168, 192, 255, 0.3);
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: #5BA9F9;
    border-color: #5BA9F9;
    color: white;
    font-weight: 600;
}

.pagination-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
    color: #6B7280;
}

.pagination-btn.page-number {
    min-width: 32px;
    padding: 6px 8px;
}

.pagination-ellipsis {
    color: #B2AFC5;
    padding: 6px 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
}

@media (max-width: 540px) {
    .pagination-container {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .pagination-info {
        text-align: center;
        order: 2;
    }
    
    .pagination-controls {
        justify-content: center;
        order: 1;
    }
    
    .pagination-btn {
        font-size: 12px;
        padding: 4px 8px;
        min-width: 28px;
        height: 28px;
    }
    
    .pagination-btn.page-number {
        min-width: 28px;
        padding: 4px 6px;
    }
}

@media (max-width: 540px) {
    .config-actions {
        flex-direction: column;
    }
    
    .config-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
    }
    
    .history-item-actions {
        flex-wrap: wrap;
        gap: 6px;
    }
    
    .telegram-send-btn,
    .discord-send-btn {
        min-width: 28px;
        height: 28px;
        padding: 4px 6px;
    }
    
    .config-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .config-item label {
        min-width: auto;
    }
    
    .masked-value {
        max-width: 100%;
        text-align: left;
        width: 100%;
    }
    
    .auto-send-toggle {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .auto-send-section {
        padding: 12px;
    }
    
    .auto-send-header h5 {
        font-size: 15px;
    }
    
    .auto-send-description {
        font-size: 13px;
    }
    
    .history-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .history-item-meta {
        align-items: flex-start;
        width: 100%;
    }
    
    .history-item-date {
        margin-bottom: 4px;
    }
    
    .history-item-actions {
        justify-content: flex-start;
    }
}

@media (max-width: 540px) {
    .membership-details {
        margin: 12px 0;
        padding: 8px;
    }
    
    .detail-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 6px 0;
    }
    
    .detail-item label {
        font-size: 13px;
    }
    
    .detail-item span {
        font-size: 13px;
    }
    
    .membership-actions {
        flex-direction: column;
    }
    
    .membership-actions .btn {
        width: 100%;
        min-width: auto;
    }
    
    .status-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .status-icon {
        font-size: 20px;
    }
}

@media (max-width: 540px) {
    .key-action-buttons {
        flex-direction: column;
    }
    
    .key-action-buttons .btn {
        width: 100%;
        min-width: auto;
    }
    
    .key-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .masked-key {
        max-width: 100%;
        word-break: break-all;
        white-space: normal;
    }
    
    .key-status-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .key-change-form .form-actions {
        flex-direction: column;
    }
    
    .key-change-form .form-actions .btn {
        width: 100%;
    }
} 
