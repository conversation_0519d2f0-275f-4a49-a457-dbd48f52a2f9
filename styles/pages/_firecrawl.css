/* Firecrawl Scraper Page Styles */

/* Dark theme enhancements */
#scrapeForm {
    background: linear-gradient(180deg, #1A1625 0%, #0F0D1A 100%);
    min-height: 400px;
    display: flex;
    flex-direction: column;
}

#scrapeForm .section-header {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin: -20px -20px 0 -20px;
    padding: 20px;
    border-radius: 12px 12px 0 0;
    flex-shrink: 0;
}

/* Main content area that will be centered */
#scrapeForm .main-content-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.scrape-container {
    background: linear-gradient(135deg, #2A1B3D 0%, #1E1A2E 100%);
    border-radius: 16px;
    padding: 40px;
    margin: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.url-input-group {
    margin-bottom: 32px;
    width: 100%;
}

.url-label {
    display: block;
    color: #E2E8F0;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    letter-spacing: 0.5px;
    text-align: left;
}

.firecrawl-url-input {
    width: 100%;
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px 20px;
    color: #F1F5F9;
    font-size: 14px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-sizing: border-box;
}

.firecrawl-url-input::placeholder {
    color: #64748B;
    opacity: 1;
}

.firecrawl-url-input:focus {
    border-color: #FF6B35;
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    background: rgba(0, 0, 0, 0.6);
}

.firecrawl-url-input:hover {
    border-color: rgba(255, 255, 255, 0.2);
    background: rgba(0, 0, 0, 0.5);
}

.scrape-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 24px;
    flex-wrap: wrap;
    width: 100%;
}

.btn-scrape-analyze {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    border: none;
    border-radius: 12px;
    padding: 16px 32px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    min-width: 200px;
    text-transform: none;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
    flex: 0 0 auto;
}

.btn-scrape-analyze:hover {
    background: linear-gradient(135deg, #FF5722 0%, #E65100 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(255, 107, 53, 0.4);
}

.btn-scrape-analyze:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.btn-scrape-analyze:disabled {
    background: linear-gradient(135deg, #64748B 0%, #475569 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.scrape-icon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
    flex-shrink: 0;
}



/* Section header styling for Firecrawl */
#scrapeForm .section-header h3 {
    background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 24px;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Loading state for scrape button */
.btn-scrape-analyze.loading {
    background: linear-gradient(135deg, #64748B 0%, #475569 100%);
    cursor: wait;
}

.btn-scrape-analyze.loading .scrape-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 480px) {
    #scrapeForm .main-content-area {
        padding: 16px;
    }
    
    .scrape-container {
        padding: 24px 16px;
        margin: 0;
    }
    
    .firecrawl-url-input {
        padding: 14px 16px;
        font-size: 14px;
    }
    
    .scrape-actions {
        flex-direction: column;
        gap: 12px;
    }
    
    .btn-scrape-analyze {
        padding: 14px 24px;
        font-size: 15px;
        min-width: 180px;
        width: 100%;
    }
}

/* Error state styling */
.firecrawl-url-input.error {
    border-color: #EF4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success state styling */
.firecrawl-url-input.success {
    border-color: #10B981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
} 