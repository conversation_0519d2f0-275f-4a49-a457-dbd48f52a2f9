.container {
    width: 520px;
    min-height: 680px;
    background: #141021;
    border-radius: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Styles for the main content area to make footer stick to bottom */
.main-content-area {
    flex-grow: 1; /* Allows this area to expand and push footer down */
    overflow-y: visible; /* Allow content to expand naturally without scrollbars */
    display: flex; /* Added to allow its children (sections) to be managed if needed, e.g. centering */
    flex-direction: column; /* Sections stack vertically */
    min-height: 0; /* Important for flex children that might scroll or have min-heights themselves */
    /* padding-bottom: 20px; /* Optional: if sections need more space from footer */
                           /* Current sections have their own padding, so this might not be needed. */
}

.results-container {
    max-height: none;
    overflow-y: visible;
    padding: 20px; /* Restore padding for the main container */
    background: #1D1A2A; /* Restore main container background */
    border-radius: 12px; /* Restore main container border radius */
    border: 2px solid #2C2738; /* Restore main container border */
    margin-bottom: 16px;
    color: #F9F9F9;
    line-height: 1.6;
}

/* Make structured analysis blend seamlessly with main container */
.results-container .structured-analysis {
    background: transparent !important; /* Remove separate background */
    color: #F9F9F9 !important;
    border: none !important; /* Remove separate border */
    border-radius: 0 !important; /* Remove separate border radius */
    padding: 0 !important; /* Remove separate padding */
    margin: 0 !important; /* Remove separate margin */
}

/* Dark theme adjustments for structured analysis in popup */
.results-container .analysis-summary,
.results-container .analysis-insights,
.results-container .analysis-recommendations,
.results-container .analysis-market,
.results-container .analysis-section-header {
    margin: 16px 0 12px 0 !important;
}

.results-container .analysis-point {
    background: #2C2738 !important;
    color: #F9F9F9 !important;
    border-left-color: #5BA9F9 !important;
}

.results-container .point-title {
    color: #F9F9F9 !important;
}

.results-container .analysis-bullet {
    color: #B2AFC5 !important;
}

.results-container .structured-analysis strong {
    color: #F9F9F9 !important;
}

.results-container .structured-analysis em {
    color: #B2AFC5 !important;
}

.results-container .structured-analysis code {
    background: #2C2738 !important;
    color: #FFD84D !important;
    border-color: #3D3A4A !important;
}

.results-container::-webkit-scrollbar {
    width: 6px;
}

.results-container::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb:hover {
    background: #4A9AE8;
}

.results-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.loading-container {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #2C2738;
    border-top: 4px solid #5BA9F9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text h3 {
    color: #F9F9F9;
    margin-bottom: 8px;
    font-size: 18px;
}

.loading-text p {
    color: #B2AFC5;
    margin-bottom: 20px;
}

.loading-progress {
    max-width: 200px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #2C2738;
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #5BA9F9, #FFD84D);
    border-radius: 3px;
    animation: progress 2s ease-in-out infinite;
}