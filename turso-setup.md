# 🚀 Turso Database Setup Guide for HustlePlug API

## Step 1: Install Turso CLI and Create Database

### Install Turso CLI
```bash
# Install Turso CLI
curl -sSfL https://get.tur.so/install.sh | bash

# Or using npm
npm install -g @turso/cli
```

### Create Account and Database
```bash
# Sign up or login
turso auth signup
# or
turso auth login

# Create your database
turso db create hustleplug-pro-keys

# Get database URL
turso db show hustleplug-pro-keys --url

# Create authentication token
turso db tokens create hustleplug-pro-keys
```

## Step 2: Database Schema

### Create Tables
```sql
-- Pro Keys table
CREATE TABLE pro_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key_hash TEXT UNIQUE NOT NULL,
    status TEXT NOT NULL DEFAULT 'active',
    tier TEXT NOT NULL DEFAULT 'pro',
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Customers table
CREATE TABLE customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    pro_key_id INTEGER,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (pro_key_id) REFERENCES pro_keys(id)
);

-- Usage tracking table
CREATE TABLE key_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pro_key_id INTEGER NOT NULL,
    used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address TEXT,
    user_agent TEXT,
    FOREIGN KEY (pro_key_id) REFERENCES pro_keys(id)
);

-- Indexes for performance
CREATE INDEX idx_pro_keys_hash ON pro_keys(key_hash);
CREATE INDEX idx_pro_keys_status ON pro_keys(status);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_key_usage_pro_key_id ON key_usage(pro_key_id);
CREATE INDEX idx_key_usage_used_at ON key_usage(used_at);
```

## Step 3: Environment Variables

Create `.env` file:
```env
# Turso Database Configuration
TURSO_DATABASE_URL=libsql://hustleplug-pro-keys-[your-org].turso.io
TURSO_AUTH_TOKEN=your_auth_token_here

# API Configuration
PORT=3000
NODE_ENV=production

# Security
PRO_SALT=AgentHustle2024ProSalt!@#$%^&*()_+SecureKey
```

## Step 4: Install Dependencies

```bash
npm install @libsql/client drizzle-orm
npm install -D drizzle-kit
```

## Step 5: Migrate Existing Data

Your current hardcoded keys will be migrated:
- `pro_demo_key_12345` → Mike Johnson (Premium)
- `premium_demo_key_67890` → Lisa Chen (Pro) 
- `expired_demo_key_11111` → David Wilson (Expired)

## Next Steps

1. Run the schema creation commands in Turso CLI
2. Update your API code to use Turso
3. Test the migration
4. Deploy to Render

## Turso CLI Commands Reference

```bash
# Connect to database shell
turso db shell hustleplug-pro-keys

# Execute SQL file
turso db shell hustleplug-pro-keys < schema.sql

# List databases
turso db list

# Show database info
turso db show hustleplug-pro-keys

# Create backup
turso db dump hustleplug-pro-keys --output backup.sql
``` 