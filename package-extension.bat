@echo off
echo Creating HustlePlug Chrome Extension Package...

REM Create timestamp for unique filename
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set mydate=%%a-%%b-%%c
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set mytime=%%a-%%b
set zipname=HustlePlug-Extension-%mydate%_%mytime%.zip

echo Packaging files into: %zipname%

REM Use Windows built-in tar to create zip
tar -a -c -f %zipname% manifest.json popup.html popup.js background.js content.js content.css config.js js styles icons

if exist %zipname% (
    echo SUCCESS: Created %zipname%
    echo File size: 
    dir %zipname% | find "%zipname%"
    echo.
    echo Ready for Chrome deployment!
    echo To install: Extract zip, go to chrome://extensions/, enable Developer mode, click Load unpacked
) else (
    echo ERROR: Failed to create zip file
)

pause 