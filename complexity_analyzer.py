#!/usr/bin/env python3
"""
Code Complexity Analyzer for JavaScript
Calculates cyclomatic complexity to identify functions that need refactoring
"""

import re
import json
import sys
from pathlib import Path
from typing import List, Dict, Any

class ComplexityAnalyzer:
    def __init__(self):
        self.functions = []
        
    def calculate_complexity(self, function_code: str) -> int:
        """Calculate cyclomatic complexity of a function"""
        # Base complexity is 1
        complexity = 1
        
        # Decision points that increase complexity
        decision_patterns = [
            r'\bif\b',           # if statements
            r'\belse\s+if\b',    # else if statements  
            r'\bwhile\b',        # while loops
            r'\bfor\b',          # for loops
            r'\bcase\b',         # switch cases
            r'\bcatch\b',        # try-catch
            r'\?\s*[^:]+\s*:',   # ternary operators
            r'&&',               # logical AND
            r'\|\|',             # logical OR
        ]
        
        for pattern in decision_patterns:
            matches = re.findall(pattern, function_code, re.IGNORECASE)
            complexity += len(matches)
        
        return complexity
    
    def extract_functions(self, content: str) -> List[Dict[str, Any]]:
        """Extract all functions from JavaScript content"""
        functions = []
        lines = content.split('\n')
        
        # Patterns to match different function declarations
        function_patterns = [
            r'function\s+(\w+)\s*\(',                    # function name()
            r'(\w+)\s*:\s*function\s*\(',                # name: function()
            r'(\w+)\s*=\s*function\s*\(',                # name = function()
            r'(\w+)\s*=\s*\([^)]*\)\s*=>\s*{',          # name = () => {}
            r'async\s+function\s+(\w+)\s*\(',            # async function name()
            r'(\w+)\s*:\s*async\s+function\s*\(',        # name: async function()
            r'(\w+)\s*=\s*async\s+function\s*\(',        # name = async function()
            r'(\w+)\s*=\s*async\s*\([^)]*\)\s*=>\s*{',   # name = async () => {}
        ]
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Skip comments and empty lines
            if not line or line.startswith('//') or line.startswith('/*'):
                i += 1
                continue
            
            function_name = None
            for pattern in function_patterns:
                match = re.search(pattern, line)
                if match:
                    function_name = match.group(1)
                    break
            
            if function_name:
                # Extract function body
                function_lines = []
                brace_count = 0
                start_line = i + 1
                
                # Find opening brace
                j = i
                while j < len(lines):
                    current_line = lines[j]
                    function_lines.append(current_line)
                    
                    # Count braces
                    brace_count += current_line.count('{') - current_line.count('}')
                    
                    # If we've closed all braces and we had at least one opening brace
                    if brace_count == 0 and '{' in ''.join(function_lines):
                        break
                    
                    j += 1
                
                function_code = '\n'.join(function_lines)
                complexity = self.calculate_complexity(function_code)
                line_count = len(function_lines)
                
                # Determine priority based on complexity
                if complexity > 15:
                    priority = 'CRITICAL'
                elif complexity > 10:
                    priority = 'HIGH'
                elif complexity > 5:
                    priority = 'MEDIUM'
                else:
                    priority = 'LOW'
                
                functions.append({
                    'name': function_name,
                    'start_line': start_line,
                    'end_line': start_line + len(function_lines) - 1,
                    'line_count': line_count,
                    'complexity': complexity,
                    'priority': priority,
                    'suggestion': self.get_refactoring_suggestion(complexity, line_count)
                })
                
                i = j + 1
            else:
                i += 1
        
        return functions
    
    def get_refactoring_suggestion(self, complexity: int, line_count: int) -> str:
        """Generate refactoring suggestions based on metrics"""
        suggestions = []
        
        if complexity > 15:
            suggestions.append("URGENT: Break into multiple smaller functions")
        elif complexity > 10:
            suggestions.append("High complexity: Consider extracting conditional logic")
        elif complexity > 5:
            suggestions.append("Moderate complexity: Look for extraction opportunities")
        
        if line_count > 100:
            suggestions.append("Function too long: Split into smaller functions")
        elif line_count > 50:
            suggestions.append("Consider breaking into smaller functions")
        
        if not suggestions:
            suggestions.append("Function complexity is acceptable")
        
        return "; ".join(suggestions)
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a JavaScript file for complexity"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            return {'error': f"Could not read file: {e}"}
        
        functions = self.extract_functions(content)
        
        # Calculate statistics
        total_functions = len(functions)
        if total_functions == 0:
            return {'error': 'No functions found in file'}
        
        complexities = [f['complexity'] for f in functions]
        avg_complexity = sum(complexities) / len(complexities)
        max_complexity = max(complexities)
        
        # Count by priority
        priority_counts = {}
        for func in functions:
            priority = func['priority']
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        # Get top complexity functions
        top_complex = sorted(functions, key=lambda x: x['complexity'], reverse=True)[:5]
        
        return {
            'file': file_path,
            'summary': {
                'total_functions': total_functions,
                'average_complexity': round(avg_complexity, 2),
                'max_complexity': max_complexity,
                'priority_counts': priority_counts
            },
            'top_complex_functions': top_complex,
            'all_functions': functions,
            'refactoring_candidates': [f for f in functions if f['priority'] in ['CRITICAL', 'HIGH']]
        }
    
    def generate_report(self, analysis: Dict[str, Any], output_file: str = None) -> str:
        """Generate a detailed complexity analysis report"""
        if 'error' in analysis:
            return f"Error: {analysis['error']}"
        
        report = []
        report.append("🔍 Code Complexity Analysis Report")
        report.append("=" * 50)
        report.append("")
        
        summary = analysis['summary']
        report.append(f"📊 Summary for {analysis['file']}:")
        report.append(f"  • Total functions: {summary['total_functions']}")
        report.append(f"  • Average complexity: {summary['average_complexity']}")
        report.append(f"  • Maximum complexity: {summary['max_complexity']}")
        report.append("")
        
        report.append("📈 Priority Distribution:")
        for priority, count in summary['priority_counts'].items():
            emoji = {'CRITICAL': '🔴', 'HIGH': '🟠', 'MEDIUM': '🟡', 'LOW': '🟢'}.get(priority, '⚪')
            report.append(f"  {emoji} {priority}: {count} functions")
        report.append("")
        
        if analysis['refactoring_candidates']:
            report.append("🚨 Refactoring Candidates (HIGH/CRITICAL Priority):")
            report.append("-" * 50)
            for func in analysis['refactoring_candidates']:
                report.append(f"📍 {func['name']} (Line {func['start_line']})")
                report.append(f"   Complexity: {func['complexity']} | Lines: {func['line_count']}")
                report.append(f"   Priority: {func['priority']}")
                report.append(f"   Suggestion: {func['suggestion']}")
                report.append("")
        
        report.append("🔝 Top 5 Most Complex Functions:")
        report.append("-" * 30)
        for i, func in enumerate(analysis['top_complex_functions'], 1):
            priority_emoji = {'CRITICAL': '🔴', 'HIGH': '🟠', 'MEDIUM': '🟡', 'LOW': '🟢'}.get(func['priority'], '⚪')
            report.append(f"{i}. {priority_emoji} {func['name']} (Complexity: {func['complexity']})")
            report.append(f"   Lines {func['start_line']}-{func['end_line']} ({func['line_count']} lines)")
            report.append(f"   {func['suggestion']}")
            report.append("")
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
            print(f"📄 Report saved to: {output_file}")
        
        return report_text

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 complexity_analyzer.py <javascript-file> [output-file]")
        print("Example: python3 complexity_analyzer.py popup.js complexity-report.txt")
        sys.exit(1)
    
    file_path = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not Path(file_path).exists():
        print(f"❌ Error: File '{file_path}' not found")
        sys.exit(1)
    
    print(f"🔍 Analyzing complexity of {file_path}...")
    
    analyzer = ComplexityAnalyzer()
    analysis = analyzer.analyze_file(file_path)
    
    if 'error' in analysis:
        print(f"❌ {analysis['error']}")
        sys.exit(1)
    
    # Generate and display report
    report = analyzer.generate_report(analysis, output_file)
    print(report)
    
    # Also save JSON data
    json_file = f"{Path(file_path).stem}-complexity.json"
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2)
    print(f"📊 JSON data saved to: {json_file}")
    
    # Summary recommendations
    candidates = len(analysis['refactoring_candidates'])
    if candidates > 0:
        print(f"\n🎯 RECOMMENDATION: {candidates} functions need immediate refactoring")
        print("   Focus on CRITICAL and HIGH priority functions first")
    else:
        print(f"\n✅ Good news! No high-complexity functions found")
        print("   Code complexity is within acceptable limits")

if __name__ == "__main__":
    main() 