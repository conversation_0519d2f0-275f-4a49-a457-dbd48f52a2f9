# 🔑 Complete Pro Key Management Guide

## Table of Contents
1. [Quick Start](#quick-start)
2. [Adding New Customers](#adding-new-customers)
3. [Managing Existing Keys](#managing-existing-keys)
4. [Deployment Process](#deployment-process)
5. [Testing & Validation](#testing--validation)
6. [Analytics & Monitoring](#analytics--monitoring)
7. [Troubleshooting](#troubleshooting)
8. [Security Best Practices](#security-best-practices)

---

## 🚀 Quick Start

### Current API Endpoint
```
https://hustleplug-pro-validation-g3j81a1ib-calel33s-projects.vercel.app/api/validate-key
```

### Demo Keys for Testing
```
Valid Pro: pro_demo_key_12345
Valid Premium: premium_demo_key_67890
Expired: expired_demo_key_11111
```

---

## 👥 Adding New Customers

### Step 1: Edit Customer List
Open `vercel-api/add-pro-key.js` and update:

```javascript
const newCustomers = [
    {
        name: '<PERSON>',
        email: '<EMAIL>',
        tier: 'pro',           // 'pro' or 'premium'
        months: 12,            // Duration in months
        customKey: null        // null = auto-generate
    }
];
```

### Step 2: Generate Keys
```bash
cd vercel-api
node add-pro-key.js
```

### Step 3: Update API
Copy generated code to `vercel-api/api/validate-key.js`

### Step 4: Deploy
```bash
vercel deploy --prod
```

---

## 🔧 Managing Existing Keys

### Renewal Process
Edit `vercel-api/manage-existing-keys.js`:

```javascript
const keyUpdates = [
    {
        plainKey: 'existing_customer_key',
        action: 'renew',
        months: 12,
        reason: 'Annual renewal'
    }
];
```

### Available Actions
- `renew` - Extend expiration date
- `upgrade` - Change to premium tier
- `suspend` - Temporarily disable
- `reactivate` - Re-enable suspended key
- `downgrade` - Change to pro tier

---

## 🧪 Testing Commands

```bash
# Quick test
node test-api.js

# Comprehensive test
node test-api-comprehensive.js

# Extension integration
node test-extension-integration.js
```

---

## 📊 What Gets Automatically Saved

When users validate their keys:

### Server Side (API)
- Usage count increments
- Last used timestamp updates

### Client Side (Extension)
- Pro status cached locally
- Membership details stored
- Works offline after validation

---

## 🔍 Troubleshooting

### Key Not Working
1. Check key format (no extra spaces)
2. Verify in API hash list
3. Test API endpoint
4. Check expiration date

### API Issues
- Check Vercel dashboard for errors
- Verify API URL in config.js
- Test with demo keys first

---

## 🔒 Security Best Practices

- ✅ Only store hashed keys in API
- ✅ Use unique, unpredictable keys
- ✅ Send keys via secure channels
- ❌ Never commit plain text keys
- ❌ Don't use predictable patterns

---

## 📋 Quick Commands

```bash
# Add customers
node add-pro-key.js

# Manage keys
node manage-existing-keys.js

# Deploy
vercel deploy --prod

# Test
node test-api.js
```

---

## 🎯 Workflow Summary

### New Customer
1. Add to add-pro-key.js
2. Generate keys
3. Update API
4. Deploy
5. Send key to customer

### Renewal
1. Add to manage-existing-keys.js
2. Run script
3. Update API
4. Deploy
5. Notify customer

### Support
1. Test with demo keys
2. Verify customer key hash
3. Check API logs
4. Fix and redeploy

This guide covers all essential key management operations! 🚀 