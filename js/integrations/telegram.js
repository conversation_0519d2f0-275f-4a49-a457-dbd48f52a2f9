// Telegram Bot API Integration Module
import { TELEGRAM_CONFIG } from '../../config.js';

/**
 * Send analysis results to Telegram
 * @param {Object} analysisData - The analysis data to send
 * @param {string} botToken - Telegram bot token
 * @param {string} chatId - Telegram chat ID
 * @returns {Promise<Object>} - Result of the send operation
 */
export async function sendAnalysisToTelegram(analysisData, botToken, chatId) {
    try {
        if (!botToken || !chatId) {
            throw new Error('Bot token and chat ID are required');
        }

        const message = formatAnalysisAsMarkdown(analysisData);
        
        // Split message if too long
        const messages = splitLongMessage(message);
        
        const results = [];
        for (const msg of messages) {
            const result = await sendTelegramMessage(botToken, chatId, msg);
            results.push(result);
            
            // Small delay between messages to avoid rate limiting
            if (messages.length > 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        return {
            success: true,
            messageCount: results.length,
            results: results
        };
        
    } catch (error) {
        console.error('Error sending to Telegram:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send a message to Telegram using Bot API
 * @param {string} botToken - Bot token
 * @param {string} chatId - Chat ID
 * @param {string} message - Message text
 * @returns {Promise<Object>} - API response
 */
async function sendTelegramMessage(botToken, chatId, message) {
    const url = `${TELEGRAM_CONFIG.API_BASE_URL}${botToken}/sendMessage`;
    
    const payload = {
        chat_id: chatId,
        text: message,
        parse_mode: TELEGRAM_CONFIG.PARSE_MODE,
        disable_web_page_preview: true
    };
    
    let lastError;
    
    for (let attempt = 1; attempt <= TELEGRAM_CONFIG.RETRY_ATTEMPTS; attempt++) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload),
                signal: AbortSignal.timeout(TELEGRAM_CONFIG.TIMEOUT)
            });
            
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.description || `HTTP ${response.status}`);
            }
            
            return {
                success: true,
                messageId: data.result.message_id,
                attempt: attempt
            };
            
        } catch (error) {
            lastError = error;
            console.warn(`Telegram send attempt ${attempt} failed:`, error.message);
            
            if (attempt < TELEGRAM_CONFIG.RETRY_ATTEMPTS) {
                // Exponential backoff
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
    
    throw lastError;
}

/**
 * Format analysis data as HTML for Telegram
 * @param {Object} analysisData - Analysis data object
 * @returns {string} - Formatted HTML message
 */
export function formatAnalysisAsMarkdown(analysisData) {
    const { analysisType, result, date } = analysisData;
    
    let message = `🤖 <b>Agent Hustle Analysis Report</b>\n\n`;
    
    // Add analysis type and date with better formatting
    message += `📊 <b>Analysis Type:</b> ${analysisType || 'General Analysis'}\n`;
    message += `📅 <b>Generated:</b> ${formatDate(date || new Date())}\n`;
    message += `\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    
    // Handle different result formats
    let content = '';
    let summary = '';
    let keyPoints = [];
    
    if (typeof result === 'object' && result !== null) {
        content = result.content || result.analysis || '';
        summary = result.summary || '';
        keyPoints = result.keyPoints || [];
    } else if (typeof result === 'string') {
        content = result;
    }
    
    // Add executive summary if available
    if (summary && summary.trim()) {
        message += `📋 <b>Executive Summary</b>\n`;
        message += `${cleanHtmlForTelegram(summary.trim())}\n\n`;
        message += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    }
    
    // Add main analysis content
    if (content && content.trim()) {
        message += `📝 <b>Detailed Analysis</b>\n`;
        
        // Clean and format the content
        const cleanContent = cleanAnalysisContent(content);
        message += `${cleanHtmlForTelegram(cleanContent)}\n\n`;
        
        if (keyPoints.length > 0 || summary) {
            message += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
        }
    }
    
    // Add key points if available
    if (keyPoints && Array.isArray(keyPoints) && keyPoints.length > 0) {
        message += `🔑 <b>Key Insights</b>\n`;
        keyPoints.forEach((point, index) => {
            const cleanPoint = point.trim();
            if (cleanPoint) {
                message += `${index + 1}. ${cleanHtmlForTelegram(cleanPoint)}\n`;
            }
        });
        message += '\n';
        message += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;
    }
    
    // Add footer with branding
    message += `🚀 <b>Powered by Agent Hustle Pro</b>\n`;
    message += `<i>Professional AI Analysis at Your Fingertips</i>`;
    
    return message;
}

/**
 * Clean and format analysis content for better readability
 * @param {string} content - Raw analysis content
 * @returns {string} - Cleaned content
 */
function cleanAnalysisContent(content) {
    if (!content || typeof content !== 'string') {
        return '';
    }
    
    let cleaned = content.trim();
    
    // Remove excessive whitespace and normalize line breaks
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n'); // Max 2 consecutive line breaks
    cleaned = cleaned.replace(/[ \t]+/g, ' '); // Normalize spaces
    
    // Improve formatting for common patterns
    cleaned = cleaned.replace(/^(\d+\.\s)/gm, '\n$1'); // Add line break before numbered lists
    cleaned = cleaned.replace(/^(-\s|\*\s|•\s)/gm, '\n$1'); // Add line break before bullet points
    
    // Clean up any HTML tags that might be present
    cleaned = cleaned.replace(/<[^>]*>/g, '');
    
    // Use more of Telegram's 4096 character limit (leave buffer for headers/footers)
    if (cleaned.length > 4000) {
        cleaned = cleaned.substring(0, 4000) + '...\n\n_[Content truncated - message too long for Telegram]_';
    }
    
    return cleaned.trim();
}

/**
 * Clean HTML for Telegram while preserving readability
 * @param {string} text - Text to clean
 * @returns {string} - Cleaned text
 */
function cleanHtmlForTelegram(text) {
    if (!text || typeof text !== 'string') {
        return '';
    }
    
    // Escape HTML special characters for Telegram
    // HTML mode is more forgiving than Markdown
    return text
        .replace(/&/g, '&amp;')   // Escape ampersands first
        .replace(/</g, '&lt;')    // Escape less than
        .replace(/>/g, '&gt;')    // Escape greater than
        .replace(/"/g, '&quot;')  // Escape quotes
        .replace(/'/g, '&#x27;'); // Escape single quotes
}

/**
 * Test Telegram connection
 * @param {string} botToken - Bot token to test
 * @param {string} chatId - Chat ID to test
 * @returns {Promise<Object>} - Test result
 */
export async function testTelegramConnection(botToken, chatId) {
    try {
        const testMessage = `🔧 <b>Test Message</b>\n\nTelegram integration is working correctly!\n\n<b>Time:</b> ${formatDate(new Date())}\n\n---\n<b>Agent Hustle Pro Analyzer</b>`;
        
        const result = await sendTelegramMessage(botToken, chatId, testMessage);
        
        return {
            success: true,
            message: 'Test message sent successfully!',
            messageId: result.messageId
        };
        
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Validate bot token format
 * @param {string} token - Bot token to validate
 * @returns {boolean} - Whether token format is valid
 */
export function validateBotToken(token) {
    if (!token || typeof token !== 'string') {
        return false;
    }
    
    // Telegram bot token format: number:alphanumeric_string
    const tokenRegex = /^\d+:[A-Za-z0-9_-]+$/;
    return tokenRegex.test(token.trim());
}

/**
 * Validate chat ID format
 * @param {string} chatId - Chat ID to validate
 * @returns {boolean} - Whether chat ID format is valid
 */
export function validateChatId(chatId) {
    if (!chatId || typeof chatId !== 'string') {
        return false;
    }
    
    const trimmed = chatId.trim();
    
    // Chat ID can be a number (positive or negative) or @username
    return /^-?\d+$/.test(trimmed) || /^@[a-zA-Z0-9_]+$/.test(trimmed);
}

/**
 * Split long messages into chunks
 * @param {string} message - Message to split
 * @returns {Array<string>} - Array of message chunks
 */
function splitLongMessage(message) {
    if (message.length <= TELEGRAM_CONFIG.MAX_MESSAGE_LENGTH) {
        return [message];
    }
    
    const chunks = [];
    let currentChunk = '';
    const lines = message.split('\n');
    
    for (const line of lines) {
        const testChunk = currentChunk + (currentChunk ? '\n' : '') + line;
        
        if (testChunk.length <= TELEGRAM_CONFIG.MAX_MESSAGE_LENGTH - 50) { // Leave some buffer
            currentChunk = testChunk;
        } else {
            if (currentChunk) {
                chunks.push(currentChunk);
                currentChunk = line;
            } else {
                // Line itself is too long, split it
                chunks.push(line.substring(0, TELEGRAM_CONFIG.MAX_MESSAGE_LENGTH - 50) + '...');
                currentChunk = '...' + line.substring(TELEGRAM_CONFIG.MAX_MESSAGE_LENGTH - 50);
            }
        }
    }
    
    if (currentChunk) {
        chunks.push(currentChunk);
    }
    
    return chunks;
}

/**
 * Format date for display
 * @param {Date} date - Date to format
 * @returns {string} - Formatted date string
 */
function formatDate(date) {
    return new Date(date).toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
} 