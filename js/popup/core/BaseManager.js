/**
 * Base Manager Class
 * Provides common functionality for all popup managers
 */
export class BaseManager {
    constructor(controller) {
        this.controller = controller;
        this.initialized = false;
    }

    /**
     * Initialize the manager
     * Override in subclasses
     */
    async init() {
        this.initialized = true;
    }

    /**
     * Get reference to other managers through controller
     */
    getManager(managerName) {
        return this.controller.getManager(managerName);
    }

    /**
     * Common error handling
     */
    handleError(error, context = '') {
        console.error(`${this.constructor.name} Error${context ? ` (${context})` : ''}:`, error);
        if (this.controller.uiManager) {
            this.controller.uiManager.showError(`${context || 'Operation'} failed: ${error.message}`);
        }
    }

    /**
     * Common success handling
     */
    handleSuccess(message) {
        if (this.controller.uiManager) {
            this.controller.uiManager.showSuccess(message);
        }
    }

    /**
     * Check if manager is initialized
     */
    ensureInitialized() {
        if (!this.initialized) {
            throw new Error(`${this.constructor.name} not initialized`);
        }
    }

    /**
     * Cleanup method - override in subclasses if needed
     */
    cleanup() {
        // Override in subclasses
    }
}
