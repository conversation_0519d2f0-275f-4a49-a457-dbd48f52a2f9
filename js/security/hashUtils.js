// Security utilities for pro key hashing
import { PRO_SALT } from '../../config.js';

/**
 * Hash a key using SHA-256 with salt for secure validation
 * @param {string} plainKey - The plain text key to hash
 * @param {string} salt - Salt to use for hashing (optional, uses default)
 * @returns {Promise<string>} - Hex string of the hash
 */
export async function hashKey(plainKey, salt = PRO_SALT) {
    try {
        const encoder = new TextEncoder();
        const data = encoder.encode(plainKey + salt);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } catch (error) {
        console.error('Error hashing key:', error);
        throw new Error('Failed to hash key');
    }
}

/**
 * Verify if a plain key matches a hash
 * @param {string} plainKey - The plain text key to verify
 * @param {string} hash - The hash to compare against
 * @param {string} salt - Salt used for hashing (optional, uses default)
 * @returns {Promise<boolean>} - True if key matches hash
 */
export async function verifyKey(plainKey, hash, salt = PRO_SALT) {
    try {
        const computedHash = await hashKey(plainKey, salt);
        return computedHash === hash;
    } catch (error) {
        console.error('Error verifying key:', error);
        return false;
    }
} 