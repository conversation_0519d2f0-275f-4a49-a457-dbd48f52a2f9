# 🎯 Universal Feature Description Generator Prompt

## **Purpose**
This prompt template helps generate comprehensive, architecture-aware feature descriptions for any software project by analyzing existing codebase patterns, constraints, and architectural decisions.

---

## 📋 **The Prompt Template**

```
I need help creating a detailed feature description for adding [FEATURE_NAME] to [PROJECT_NAME]. 

Please analyze the current project architecture and create a comprehensive feature specification that follows established patterns and constraints.

**ANALYSIS PHASE - Please complete these steps first:**

1. **🔍 Architecture Discovery**
   - Search the codebase to understand the overall architecture pattern
   - Identify the main technologies, frameworks, and design patterns used
   - Look for existing file organization and naming conventions
   - Find any architectural documentation or README files

2. **📁 Structure Analysis** 
   - Examine the current folder structure and file organization
   - Identify where similar features are implemented
   - Look for any size limits, coding standards, or style guides
   - Check for existing manager/service/component patterns

3. **🧩 Integration Points**
   - Find how existing features are integrated into the main application
   - Look for navigation patterns, routing, or section management
   - Identify event handling and state management approaches
   - Check API integration patterns and data flow

4. **🎨 UI/UX Patterns**
   - Examine existing UI components and styling approaches
   - Look for design system, CSS organization, or component libraries
   - Check responsive design patterns and accessibility considerations
   - Find any existing modal, form, or interaction patterns

5. **🔐 Security & Constraints**
   - Identify authentication/authorization patterns
   - Look for data storage and privacy considerations
   - Find any rate limiting, validation, or security measures
   - Check for tier-based features (free vs premium)

**FEATURE SPECIFICATION - Based on your analysis, create:**

## 📋 Feature Overview
- **Feature Name**: [Clear, descriptive name]
- **Purpose**: [What problem it solves]
- **User Value**: [Why users want this feature]
- **Integration Approach**: [How it fits into existing architecture]

## 🏗️ Technical Architecture
- **Implementation Pattern**: [Following project's established patterns]
- **File Structure**: [Where files should be placed, following naming conventions]
- **Dependencies**: [What existing components/services it depends on]
- **Size Constraints**: [Adherence to any file size or complexity limits]

## 🎨 User Interface Design
- **UI Components**: [Detailed component structure following existing patterns]
- **Navigation Integration**: [How users access the feature]
- **Responsive Design**: [Mobile/desktop considerations]
- **Accessibility**: [A11y considerations following project standards]

## ⚙️ Feature Functionality
- **Core Features**: [Essential functionality list]
- **Advanced Features**: [Nice-to-have or premium features]
- **User Workflows**: [Step-by-step user interactions]
- **Edge Cases**: [Error handling and edge case management]

## 🔌 Integration Details
- **API Integration**: [How it connects to backend/external services]
- **Data Management**: [Storage, caching, and data flow]
- **State Management**: [How state is managed following project patterns]
- **Event Handling**: [User interactions and system events]

## 💾 Data Architecture
- **Storage Strategy**: [Local storage, database, cache strategies]
- **Data Models**: [Structure of data objects]
- **Migration Strategy**: [How to handle existing data]
- **Backup/Export**: [Data portability considerations]

## 🎛️ Implementation Structure
- **Code Organization**: [Specific files and their responsibilities]
- **Class/Function Design**: [Following project's OOP or functional patterns]
- **Configuration**: [Settings and customization options]
- **Testing Strategy**: [Unit, integration, and E2E testing approaches]

## 🎨 Styling & Design
- **CSS Architecture**: [Following existing styling patterns]
- **Component Styling**: [Specific CSS classes and design tokens]
- **Theme Integration**: [Dark/light mode, color schemes]
- **Animation/Interactions**: [Motion design following project style]

## 🔧 Developer Experience
- **Setup Instructions**: [How to implement the feature]
- **Configuration Options**: [Customizable settings]
- **Debug/Development Tools**: [Testing and debugging approaches]
- **Documentation Needs**: [What docs need updating]

## 🛡️ Security & Privacy
- **Authentication**: [How the feature handles auth]
- **Data Protection**: [Privacy and security measures]
- **Validation**: [Input validation and sanitization]
- **Rate Limiting**: [Abuse prevention measures]

## 📊 Tiered Features (if applicable)
- **Free Tier**: [What's available to all users]
- **Premium Tier**: [What requires paid access]
- **Usage Limits**: [Any restrictions or quotas]
- **Upgrade Prompts**: [How to encourage premium adoption]

## 🧪 Testing Strategy
- **Test Coverage**: [What needs to be tested]
- **Test Organization**: [Where tests should be placed]
- **Mock Data**: [Test data requirements]
- **Performance Tests**: [Load and performance considerations]

## 📈 Analytics & Monitoring
- **Usage Metrics**: [What to track]
- **Performance Monitoring**: [Performance indicators]
- **Error Tracking**: [Error reporting and handling]
- **User Feedback**: [How to gather user input]

## 🚀 Implementation Plan
- **Phase 1**: [Core functionality - Week 1-2]
- **Phase 2**: [Enhanced features - Week 3-4]  
- **Phase 3**: [Polish & optimization - Week 5]
- **Dependencies**: [What needs to be completed first]

## 📋 Definition of Done
- [ ] Core functionality implemented and tested
- [ ] UI/UX matches project design standards
- [ ] Security and privacy requirements met
- [ ] Documentation updated
- [ ] Performance benchmarks met
- [ ] Accessibility standards compliance
- [ ] Cross-browser/device testing completed

---

**IMPORTANT REQUIREMENTS:**
- Follow ALL existing architectural patterns and constraints
- Maintain backward compatibility with existing features
- Respect file size limits and code organization rules
- Include comprehensive error handling and edge cases
- Provide specific, actionable implementation details
- Consider both technical and user experience perspectives
- Include realistic time estimates and dependencies
```

---

## 🔧 **How to Use This Prompt**

### **Step 1: Customize the Variables**
Replace these placeholders with your specific details:
- `[FEATURE_NAME]` - The feature you want to add (e.g., "AI Chat", "Dark Mode", "Export Tool")
- `[PROJECT_NAME]` - Your project name (e.g., "React Dashboard", "Chrome Extension")

### **Step 2: Provide Context**
Include any relevant context about:
- Project constraints or requirements
- User feedback or feature requests
- Technical limitations or preferences
- Timeline or resource constraints

### **Step 3: Iterate and Refine**
- Review the generated specification
- Ask follow-up questions for clarification
- Request specific implementation details
- Validate against your project's unique needs

---

## 🎯 **Example Usage**

```
I need help creating a detailed feature description for adding "Dark Mode Toggle" to "TaskFlow Dashboard". 

This is a React-based project management dashboard with TypeScript, using Tailwind CSS for styling and Zustand for state management. The app has a component-based architecture with strict ESLint rules and 200-line file limits.

[Then include the full analysis prompt above]
```

---

## 🧠 **Key Success Factors**

### **What Made My Analysis Effective:**

1. **🔍 Comprehensive Codebase Analysis**
   - I searched for existing patterns before designing anything new
   - I identified the manager-based architecture and followed it exactly
   - I found file size limits and respected them
   - I discovered the navigation system and integrated properly

2. **📐 Architecture-First Approach**
   - I understood the BaseManager pattern before proposing implementation
   - I followed the PopupController registration system
   - I respected the established folder structure and naming conventions
   - I identified dependencies and initialization order

3. **🎨 UI/UX Pattern Matching**
   - I examined existing sections to understand UI patterns
   - I followed the established CSS structure and styling approach
   - I integrated with the existing navigation and event handling
   - I maintained design consistency with current components

4. **💾 Data Flow Understanding**
   - I analyzed how other features store and manage data
   - I followed the Chrome storage patterns already established
   - I understood the API integration approach
   - I respected the pro/free tier distinction patterns

5. **🔐 Security & Constraint Awareness**
   - I identified the pro feature validation patterns
   - I understood the API authentication approach
   - I followed the established error handling patterns
   - I respected the privacy and storage considerations

### **What to Look for in Any Project:**

- **Architecture Patterns**: How is code organized? MVC? Component-based? Service-oriented?
- **File Organization**: Where do similar features live? What's the naming convention?
- **Size Constraints**: Are there line limits? File size restrictions? Complexity limits?
- **Integration Points**: How do features connect? Events? Direct calls? Message passing?
- **UI Patterns**: How is the interface structured? Component libraries? CSS approach?
- **Data Management**: How is data stored? APIs? Local storage? State management?
- **Security Model**: Authentication? Authorization? Validation patterns?
- **Testing Approach**: Unit tests? Integration tests? Where are they located?

---

## 🎯 **Output Quality Checklist**

A good feature description should include:

- [ ] **Specific Implementation Details** - Not just "add a button" but exactly where and how
- [ ] **Architectural Compliance** - Follows existing patterns perfectly
- [ ] **Complete User Experience** - From discovery to daily usage
- [ ] **Technical Specifications** - APIs, data models, file structure
- [ ] **Security Considerations** - Auth, validation, privacy
- [ ] **Testing Strategy** - What and how to test
- [ ] **Performance Impact** - Memory, storage, network considerations
- [ ] **Accessibility** - Works for all users
- [ ] **Responsive Design** - Works on all devices
- [ ] **Error Handling** - Graceful failure modes
- [ ] **Documentation Needs** - What docs need updating
- [ ] **Implementation Timeline** - Realistic phases and estimates

---

This prompt template will help you generate comprehensive, architecture-aware feature descriptions for any software project by following the same systematic analysis approach I used for the Agent Hustle chat feature. 